syntax = "proto3";

package com.asi.data_integration.grpc;

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/asi-system/data-integration/proto";
option java_package = "com.asi.data_integration.grpc";
option java_outer_classname = "EnhancedDataIntegrationProto";

// Enhanced Data Integration Service for ASI System
service EnhancedDataIntegrationService {
  // Ingest data with advanced ML and semantic features
  rpc IngestData(IngestDataRequest) returns (IngestDataResponse);
  
  // Query data with semantic and ML-based filtering
  rpc QueryData(QueryDataRequest) returns (QueryDataResponse);
  
  // Stream real-time data with AGI metadata
  rpc StreamData(StreamDataRequest) returns (stream DataRecord);
  
  // Get semantic annotations for data
  rpc GetSemanticAnnotations(SemanticAnnotationRequest) returns (SemanticAnnotationResponse);
  
  // Perform ML feature extraction
  rpc ExtractMLFeatures(MLFeatureRequest) returns (MLFeatureResponse);
  
  // Get data lineage graph
  rpc GetLineageGraph(LineageGraphRequest) returns (LineageGraphResponse);
  
  // Validate data against AGI requirements
  rpc ValidateAGIData(AGIValidationRequest) returns (AGIValidationResponse);
  
  // Update schema evolution
  rpc UpdateSchemaEvolution(SchemaEvolutionRequest) returns (SchemaEvolutionResponse);
}

// Enhanced data record with AGI capabilities
message DataRecord {
  string id = 1;
  DataMetadata metadata = 2;
  bytes payload = 3;
  SemanticAnnotations semantic_annotations = 4;
  MLFeatures ml_features = 5;
  repeated DataRelationship relationships = 6;
  DataLineage lineage = 7;
  repeated DataValidationResult validation_results = 8;
  AGIMetadata agi_metadata = 9;
  SchemaEvolution schema_evolution = 10;
}

// Enhanced metadata with AGI source types
message DataMetadata {
  string source_id = 1;
  DataSourceType source_type = 2;
  DataFormat format = 3;
  google.protobuf.Timestamp timestamp = 4;
  google.protobuf.Timestamp ingested_at = 5;
  google.protobuf.Timestamp processed_at = 6;
  string schema_version = 7;
  DataQuality quality_level = 8;
  double confidence_score = 9;
  map<string, string> tags = 10;
  string checksum = 11;
  int64 size_bytes = 12;
  string correlation_id = 13;
  string tenant_id = 14;
  SecurityLevel security_classification = 15;
  RetentionPolicy retention_policy = 16;
}

// Enhanced source types for AGI systems
enum DataSourceType {
  DATABASE = 0;
  API = 1;
  FILE = 2;
  STREAM = 3;
  DEVICE = 4;
  CLOUD = 5;
  SENSOR = 6;
  ML_MODEL = 7;
  KNOWLEDGE_GRAPH = 8;
  NEURAL_NETWORK = 9;
  REASONING_ENGINE = 10;
  HUMAN_FEEDBACK = 11;
  SYNTHETIC_DATA = 12;
  EDGE_DEVICE = 13;
  QUANTUM_SENSOR = 14;
  MULTIMODAL_INPUT = 15;
}

// Enhanced data formats for ML/AI
enum DataFormat {
  JSON = 0;
  XML = 1;
  CSV = 2;
  AVRO = 3;
  PARQUET = 4;
  PROTOBUF = 5;
  BINARY = 6;
  TENSOR = 7;
  EMBEDDING = 8;
  GRAPH_ML = 9;
  SEMANTIC_RDF = 10;
  NEURAL_WEIGHTS = 11;
  ATTENTION_MAPS = 12;
  FEATURE_VECTORS = 13;
  KNOWLEDGE_TRIPLES = 14;
  MULTIMODAL_FUSION = 15;
}

// Enhanced quality levels for AGI
enum DataQuality {
  RAW = 0;
  VALIDATED = 1;
  NORMALIZED = 2;
  ENRICHED = 3;
  VERIFIED = 4;
  ML_READY = 5;
  FEATURE_ENGINEERED = 6;
  SEMANTICALLY_ANNOTATED = 7;
  KNOWLEDGE_ENHANCED = 8;
  AGI_OPTIMIZED = 9;
}

// Security classification levels
enum SecurityLevel {
  PUBLIC = 0;
  INTERNAL = 1;
  CONFIDENTIAL = 2;
  RESTRICTED = 3;
  TOP_SECRET = 4;
}

// Retention policy for data lifecycle
message RetentionPolicy {
  int32 retention_days = 1;
  int32 archive_after_days = 2;
  int32 delete_after_days = 3;
}

// Semantic annotations for AGI understanding
message SemanticAnnotations {
  repeated OntologyTerm ontology_terms = 1;
  repeated NamedEntity entities = 2;
  repeated ConceptAnnotation concepts = 3;
  repeated EmbeddingVector embeddings = 4;
}

message OntologyTerm {
  string uri = 1;
  string label = 2;
  double confidence = 3;
  string source = 4;
}

message NamedEntity {
  string text = 1;
  string entity_type = 2;
  int32 start_offset = 3;
  int32 end_offset = 4;
  double confidence = 5;
  string linked_entity_id = 6;
}

message ConceptAnnotation {
  string concept_id = 1;
  string concept_name = 2;
  double relevance_score = 3;
  string context_window = 4;
}

message EmbeddingVector {
  string model_name = 1;
  int32 vector_dimension = 2;
  repeated float vector = 3;
  EmbeddingType vector_type = 4;
  double similarity_threshold = 5;
}

enum EmbeddingType {
  TEXT_EMBEDDING = 0;
  IMAGE_EMBEDDING = 1;
  AUDIO_EMBEDDING = 2;
  MULTIMODAL_EMBEDDING = 3;
  KNOWLEDGE_EMBEDDING = 4;
  BEHAVIORAL_EMBEDDING = 5;
}

// ML features for model training and inference
message MLFeatures {
  map<string, FeatureVector> feature_vectors = 1;
  map<string, string> categorical_features = 2;
  map<string, double> numerical_features = 3;
  map<string, FeatureVector> temporal_features = 4;
  map<string, double> feature_importance = 5;
  FeatureEngineering feature_engineering = 6;
}

message FeatureVector {
  repeated double values = 1;
}

message FeatureEngineering {
  repeated string transformations = 1;
  string scaling_method = 2;
  string encoding_method = 3;
  string dimensionality_reduction = 4;
}

// Enhanced relationships with AGI relationship types
message DataRelationship {
  RelationshipType relationship_type = 1;
  string target_id = 2;
  string target_type = 3;
  map<string, string> properties = 4;
  double confidence_score = 5;
  double weight = 6;
  bool bidirectional = 7;
  TemporalValidity temporal_validity = 8;
  double evidence_score = 9;
  DiscoveryMethod discovery_method = 10;
}

enum RelationshipType {
  DERIVED_FROM = 0;
  SIMILAR_TO = 1;
  PART_OF = 2;
  CONTAINS = 3;
  REFERENCES = 4;
  TEMPORAL_SEQUENCE = 5;
  CAUSAL_RELATIONSHIP = 6;
  SEMANTIC_SIMILARITY = 7;
  FEATURE_CORRELATION = 8;
  KNOWLEDGE_LINK = 9;
  INFERENCE_CHAIN = 10;
  ATTENTION_WEIGHT = 11;
  NEURAL_PATHWAY = 12;
  REASONING_STEP = 13;
  FEEDBACK_LOOP = 14;
}

message TemporalValidity {
  google.protobuf.Timestamp valid_from = 1;
  google.protobuf.Timestamp valid_to = 2;
}

enum DiscoveryMethod {
  MANUAL_ANNOTATION = 0;
  RULE_BASED = 1;
  ML_INFERENCE = 2;
  STATISTICAL_CORRELATION = 3;
  SEMANTIC_ANALYSIS = 4;
  GRAPH_MINING = 5;
  NEURAL_ATTENTION = 6;
  CAUSAL_INFERENCE = 7;
  KNOWLEDGE_GRAPH_REASONING = 8;
}

// Enhanced data lineage with resource tracking
message DataLineage {
  string original_source = 1;
  repeated ProcessingStep processing_steps = 2;
  repeated string derived_from = 3;
  repeated string influences = 4;
  google.protobuf.Timestamp created_at = 5;
  string created_by = 6;
  LineageGraph lineage_graph = 7;
}

message ProcessingStep {
  string step_id = 1;
  ProcessingStepType step_type = 2;
  string processor = 3;
  string processor_version = 4;
  google.protobuf.Timestamp processed_at = 5;
  int64 processing_duration_ms = 6;
  map<string, string> parameters = 7;
  ProcessingStatus status = 8;
  string error_message = 9;
  map<string, double> performance_metrics = 10;
  ResourceUsage resource_usage = 11;
}

enum ProcessingStepType {
  DATA_INGESTION = 0;
  DATA_VALIDATION = 1;
  DATA_NORMALIZATION = 2;
  FEATURE_EXTRACTION = 3;
  ML_TRAINING = 4;
  ML_INFERENCE = 5;
  SEMANTIC_ANNOTATION = 6;
  KNOWLEDGE_ENRICHMENT = 7;
  REASONING_STEP = 8;
  DECISION_MAKING = 9;
  SELF_IMPROVEMENT = 10;
  HUMAN_FEEDBACK = 11;
  QUALITY_ASSESSMENT = 12;
  SECURITY_SCAN = 13;
  COMPLIANCE_CHECK = 14;
}

enum ProcessingStatus {
  PENDING = 0;
  PROCESSING = 1;
  COMPLETED = 2;
  FAILED = 3;
  RETRYING = 4;
  SKIPPED = 5;
  CANCELLED = 6;
  OPTIMIZED = 7;
}

message ResourceUsage {
  int64 cpu_time_ms = 1;
  int64 memory_usage_bytes = 2;
  int64 gpu_time_ms = 3;
  int64 network_bytes_transferred = 4;
}

message LineageGraph {
  repeated LineageNode nodes = 1;
  repeated LineageEdge edges = 2;
}

message LineageNode {
  string node_id = 1;
  string node_type = 2;
  map<string, string> properties = 3;
}

message LineageEdge {
  string source_node_id = 1;
  string target_node_id = 2;
  string edge_type = 3;
  double weight = 4;
}

// Enhanced validation with AGI-specific categories
message DataValidationResult {
  string rule_id = 1;
  string rule_name = 2;
  ValidationCategory rule_category = 3;
  bool passed = 4;
  ValidationSeverity severity = 5;
  string error_message = 6;
  double confidence_score = 7;
  map<string, string> details = 8;
  string suggested_fix = 9;
  google.protobuf.Timestamp validated_at = 10;
  string validator_version = 11;
}

enum ValidationCategory {
  SCHEMA_VALIDATION = 0;
  DATA_QUALITY = 1;
  BUSINESS_RULES = 2;
  SECURITY_COMPLIANCE = 3;
  ML_READINESS = 4;
  SEMANTIC_CONSISTENCY = 5;
  ETHICAL_COMPLIANCE = 6;
  PRIVACY_PROTECTION = 7;
  BIAS_DETECTION = 8;
  FAIRNESS_CHECK = 9;
}

enum ValidationSeverity {
  INFO = 0;
  WARNING = 1;
  ERROR = 2;
  CRITICAL = 3;
  BLOCKING = 4;
}

// AGI-specific metadata for cognitive processing
message AGIMetadata {
  double cognitive_complexity = 1;
  int32 reasoning_depth = 2;
  double multimodality_score = 3;
  AbstractionLevel abstraction_level = 4;
  double learning_potential = 5;
  double novelty_score = 6;
  repeated string ethical_implications = 7;
  map<string, double> bias_indicators = 8;
  UncertaintyMeasures uncertainty_measures = 9;
  ExplainabilityMetrics explainability_metrics = 10;
}

enum AbstractionLevel {
  RAW_SENSORY = 0;
  FEATURE_LEVEL = 1;
  CONCEPT_LEVEL = 2;
  ABSTRACT_REASONING = 3;
  META_COGNITIVE = 4;
  PHILOSOPHICAL = 5;
}

message UncertaintyMeasures {
  double epistemic = 1;
  double aleatoric = 2;
  double total = 3;
}

message ExplainabilityMetrics {
  double interpretability_score = 1;
  bool feature_importance_available = 2;
  bool attention_maps_available = 3;
  bool causal_explanation_available = 4;
}

// Schema evolution tracking
message SchemaEvolution {
  repeated SchemaChange evolution_history = 1;
  CompatibilityLevel compatibility_level = 2;
  bool migration_required = 3;
  string migration_script = 4;
}

message SchemaChange {
  string change_id = 1;
  SchemaChangeType change_type = 2;
  string field_path = 3;
  string old_value = 4;
  string new_value = 5;
  google.protobuf.Timestamp changed_at = 6;
  string changed_by = 7;
  string reason = 8;
}

enum SchemaChangeType {
  FIELD_ADDED = 0;
  FIELD_REMOVED = 1;
  FIELD_RENAMED = 2;
  TYPE_CHANGED = 3;
  ENUM_VALUE_ADDED = 4;
  ENUM_VALUE_REMOVED = 5;
  DEFAULT_CHANGED = 6;
  DOCUMENTATION_UPDATED = 7;
}

enum CompatibilityLevel {
  BACKWARD = 0;
  FORWARD = 1;
  FULL = 2;
  NONE = 3;
  BACKWARD_TRANSITIVE = 4;
  FORWARD_TRANSITIVE = 5;
  FULL_TRANSITIVE = 6;
}

// Request/Response messages
message IngestDataRequest {
  repeated DataRecord records = 1;
  bool validate_schema = 2;
  bool extract_features = 3;
  bool generate_embeddings = 4;
}

message IngestDataResponse {
  repeated string ingested_ids = 1;
  repeated DataValidationResult validation_results = 2;
  int32 success_count = 3;
  int32 error_count = 4;
}

message QueryDataRequest {
  string query = 1;
  repeated string filters = 2;
  int32 limit = 3;
  int32 offset = 4;
  bool include_embeddings = 5;
  bool include_lineage = 6;
}

message QueryDataResponse {
  repeated DataRecord records = 1;
  int32 total_count = 2;
  bool has_more = 3;
}

message StreamDataRequest {
  repeated string topics = 1;
  repeated string filters = 2;
  bool include_agi_metadata = 3;
}

message SemanticAnnotationRequest {
  string data_id = 1;
  repeated string annotation_types = 2;
}

message SemanticAnnotationResponse {
  SemanticAnnotations annotations = 1;
}

message MLFeatureRequest {
  string data_id = 1;
  repeated string feature_types = 2;
}

message MLFeatureResponse {
  MLFeatures features = 1;
}

message LineageGraphRequest {
  string data_id = 1;
  int32 depth = 2;
  bool include_resource_usage = 3;
}

message LineageGraphResponse {
  LineageGraph graph = 1;
}

message AGIValidationRequest {
  DataRecord record = 1;
  repeated string validation_rules = 2;
}

message AGIValidationResponse {
  repeated DataValidationResult results = 1;
  bool passed = 2;
}

message SchemaEvolutionRequest {
  SchemaChange change = 1;
  bool dry_run = 2;
}

message SchemaEvolutionResponse {
  bool success = 1;
  string migration_script = 2;
  repeated string warnings = 3;
}
