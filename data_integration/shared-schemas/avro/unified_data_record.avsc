{"type": "record", "name": "UnifiedDataRecord", "namespace": "com.asi.data_integration.avro", "doc": "Enhanced unified data record for ASI data integration pipeline with AGI/ASI capabilities", "fields": [{"name": "id", "type": "string", "doc": "Unique identifier for the data record (UUID v4)"}, {"name": "metadata", "type": {"type": "record", "name": "DataMetadata", "fields": [{"name": "sourceId", "type": "string", "doc": "Identifier of the data source"}, {"name": "sourceType", "type": {"type": "enum", "name": "DataSourceType", "symbols": ["DATABASE", "API", "FILE", "STREAM", "DEVICE", "CLOUD", "SENSOR", "ML_MODEL", "KNOWLEDGE_GRAPH", "NEURAL_NETWORK", "REASONING_ENGINE", "HUMAN_FEEDBACK", "SYNTHETIC_DATA", "EDGE_DEVICE", "QUANTUM_SENSOR", "MULTIMODAL_INPUT"]}}, {"name": "format", "type": {"type": "enum", "name": "DataFormat", "symbols": ["JSON", "XML", "CSV", "AVRO", "PARQUET", "PROTOBUF", "BINARY", "TENSOR", "EMBEDDING", "GRAPH_ML", "SEMANTIC_RDF", "NEURAL_WEIGHTS", "ATTENTION_MAPS", "FEATURE_VECTORS", "KNOWLEDGE_TRIPLES", "MULTIMODAL_FUSION"]}}, {"name": "timestamp", "type": {"type": "long", "logicalType": "timestamp-millis"}, "doc": "Original timestamp of the data"}, {"name": "ingestedAt", "type": {"type": "long", "logicalType": "timestamp-millis"}, "doc": "Timestamp when data was ingested"}, {"name": "processedAt", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null, "doc": "Timestamp when data was last processed"}, {"name": "schemaVersion", "type": "string", "doc": "Version of the schema used (semantic versioning)"}, {"name": "qualityLevel", "type": {"type": "enum", "name": "DataQuality", "symbols": ["RAW", "VALIDATED", "NORMALIZED", "ENRICHED", "VERIFIED", "ML_READY", "FEATURE_ENGINEERED", "SEMANTICALLY_ANNOTATED", "KNOWLEDGE_ENHANCED", "AGI_OPTIMIZED"]}}, {"name": "confidenceScore", "type": "double", "default": 1.0, "doc": "Confidence score for data quality and reliability (0.0-1.0)"}, {"name": "tags", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "checksum", "type": ["null", "string"], "default": null}, {"name": "sizeBytes", "type": "long", "default": 0}, {"name": "correlationId", "type": ["null", "string"], "default": null}, {"name": "tenantId", "type": ["null", "string"], "default": null}, {"name": "securityClassification", "type": {"type": "enum", "name": "SecurityLevel", "symbols": ["PUBLIC", "INTERNAL", "CONFIDENTIAL", "RESTRICTED", "TOP_SECRET"]}, "default": "INTERNAL"}, {"name": "retentionPolicy", "type": {"type": "record", "name": "RetentionPolicy", "fields": [{"name": "retentionDays", "type": "int", "default": 365}, {"name": "archiveAfterDays", "type": "int", "default": 90}, {"name": "deleteAfterDays", "type": ["null", "int"], "default": null}]}, "default": {"retentionDays": 365, "archiveAfterDays": 90, "deleteAfterDays": null}}]}}, {"name": "payload", "type": ["null", "string", "bytes"], "doc": "The actual data payload (JSON string or binary data)"}, {"name": "semanticAnnotations", "type": {"type": "record", "name": "SemanticAnnotations", "fields": [{"name": "ontologyTerms", "type": {"type": "array", "items": {"type": "record", "name": "OntologyTerm", "fields": [{"name": "uri", "type": "string", "doc": "URI of the ontology term"}, {"name": "label", "type": "string", "doc": "Human-readable label"}, {"name": "confidence", "type": "double", "default": 1.0}, {"name": "source", "type": "string", "doc": "Source of the annotation (manual, ML model, etc.)"}]}}, "default": []}, {"name": "entities", "type": {"type": "array", "items": {"type": "record", "name": "NamedEntity", "fields": [{"name": "text", "type": "string"}, {"name": "entityType", "type": "string"}, {"name": "startOffset", "type": "int"}, {"name": "endOffset", "type": "int"}, {"name": "confidence", "type": "double", "default": 1.0}, {"name": "linkedEntityId", "type": ["null", "string"], "default": null}]}}, "default": []}, {"name": "concepts", "type": {"type": "array", "items": {"type": "record", "name": "ConceptAnnotation", "fields": [{"name": "conceptId", "type": "string"}, {"name": "conceptName", "type": "string"}, {"name": "relevanceScore", "type": "double", "default": 1.0}, {"name": "contextWindow", "type": ["null", "string"], "default": null}]}}, "default": []}, {"name": "embeddings", "type": {"type": "array", "items": {"type": "record", "name": "EmbeddingVector", "fields": [{"name": "modelName", "type": "string", "doc": "Name/version of the embedding model"}, {"name": "vectorDimension", "type": "int"}, {"name": "vector", "type": {"type": "array", "items": "float"}}, {"name": "vectorType", "type": {"type": "enum", "name": "EmbeddingType", "symbols": ["TEXT_EMBEDDING", "IMAGE_EMBEDDING", "AUDIO_EMBEDDING", "MULTIMODAL_EMBEDDING", "KNOWLEDGE_EMBEDDING", "BEHAVIORAL_EMBEDDING"]}}, {"name": "similarity_threshold", "type": "double", "default": 0.8}]}}, "default": []}]}, "default": {"ontologyTerms": [], "entities": [], "concepts": [], "embeddings": []}}, {"name": "mlFeatures", "type": {"type": "record", "name": "MLFeatures", "fields": [{"name": "featureVectors", "type": {"type": "map", "values": {"type": "array", "items": "double"}}, "default": {}}, {"name": "categoricalFeatures", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "numericalFeatures", "type": {"type": "map", "values": "double"}, "default": {}}, {"name": "temporalFeatures", "type": {"type": "map", "values": {"type": "array", "items": "double"}}, "default": {}}, {"name": "featureImportance", "type": {"type": "map", "values": "double"}, "default": {}}, {"name": "featureEngineering", "type": {"type": "record", "name": "FeatureEngineering", "fields": [{"name": "transformations", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "scalingMethod", "type": ["null", "string"], "default": null}, {"name": "encodingMethod", "type": ["null", "string"], "default": null}, {"name": "dimensionalityReduction", "type": ["null", "string"], "default": null}]}, "default": {"transformations": [], "scalingMethod": null, "encodingMethod": null, "dimensionalityReduction": null}}]}, "default": {"featureVectors": {}, "categoricalFeatures": {}, "numericalFeatures": {}, "temporalFeatures": {}, "featureImportance": {}, "featureEngineering": {"transformations": [], "scalingMethod": null, "encodingMethod": null, "dimensionalityReduction": null}}}, {"name": "relationships", "type": {"type": "array", "items": {"type": "record", "name": "DataRelationship", "fields": [{"name": "relationshipType", "type": {"type": "enum", "name": "RelationshipType", "symbols": ["DERIVED_FROM", "SIMILAR_TO", "PART_OF", "CONTAINS", "REFERENCES", "TEMPORAL_SEQUENCE", "CAUSAL_RELATIONSHIP", "SEMANTIC_SIMILARITY", "FEATURE_CORRELATION", "KNOWLEDGE_LINK", "INFERENCE_CHAIN", "ATTENTION_WEIGHT", "NEURAL_PATHWAY", "REASONING_STEP", "FEEDBACK_LOOP"]}}, {"name": "targetId", "type": "string"}, {"name": "targetType", "type": "string"}, {"name": "properties", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "confidenceScore", "type": "double", "default": 1.0}, {"name": "weight", "type": "double", "default": 1.0, "doc": "Relationship strength/weight for graph algorithms"}, {"name": "bidirectional", "type": "boolean", "default": false}, {"name": "temporalValidity", "type": ["null", {"type": "record", "name": "TemporalValidity", "fields": [{"name": "validFrom", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "validTo", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}]}], "default": null}, {"name": "evidenceScore", "type": "double", "default": 1.0, "doc": "Evidence strength supporting this relationship"}, {"name": "discoveryMethod", "type": {"type": "enum", "name": "DiscoveryMethod", "symbols": ["MANUAL_ANNOTATION", "RULE_BASED", "ML_INFERENCE", "STATISTICAL_CORRELATION", "SEMANTIC_ANALYSIS", "GRAPH_MINING", "NEURAL_ATTENTION", "CAUSAL_INFERENCE", "KNOWLEDGE_GRAPH_REASONING"]}, "default": "MANUAL_ANNOTATION"}]}}, "default": []}, {"name": "lineage", "type": ["null", {"type": "record", "name": "DataLineage", "fields": [{"name": "originalSource", "type": "string"}, {"name": "processingSteps", "type": {"type": "array", "items": {"type": "record", "name": "ProcessingStep", "fields": [{"name": "stepId", "type": "string"}, {"name": "stepType", "type": {"type": "enum", "name": "ProcessingStepType", "symbols": ["DATA_INGESTION", "DATA_VALIDATION", "DATA_NORMALIZATION", "FEATURE_EXTRACTION", "ML_TRAINING", "ML_INFERENCE", "SEMANTIC_ANNOTATION", "KNOWLEDGE_ENRICHMENT", "REASONING_STEP", "DECISION_MAKING", "SELF_IMPROVEMENT", "HUMAN_FEEDBACK", "QUALITY_ASSESSMENT", "SECURITY_SCAN", "COMPLIANCE_CHECK"]}}, {"name": "processor", "type": "string"}, {"name": "processorVersion", "type": "string", "default": "unknown"}, {"name": "processedAt", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "processingDurationMs", "type": "long", "default": 0}, {"name": "parameters", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "status", "type": {"type": "enum", "name": "ProcessingStatus", "symbols": ["PENDING", "PROCESSING", "COMPLETED", "FAILED", "RETRYING", "SKIPPED", "CANCELLED", "OPTIMIZED"]}}, {"name": "errorMessage", "type": ["null", "string"], "default": null}, {"name": "performanceMetrics", "type": {"type": "map", "values": "double"}, "default": {}}, {"name": "resourceUsage", "type": {"type": "record", "name": "ResourceUsage", "fields": [{"name": "cpuTimeMs", "type": "long", "default": 0}, {"name": "memoryUsageBytes", "type": "long", "default": 0}, {"name": "gpuTimeMs", "type": "long", "default": 0}, {"name": "networkBytesTransferred", "type": "long", "default": 0}]}, "default": {"cpuTimeMs": 0, "memoryUsageBytes": 0, "gpuTimeMs": 0, "networkBytesTransferred": 0}}]}}, "default": []}, {"name": "derivedFrom", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "influences", "type": {"type": "array", "items": "string"}, "default": [], "doc": "Data records that this record influences or affects"}, {"name": "createdAt", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "created<PERSON>y", "type": "string"}, {"name": "lineageGraph", "type": ["null", {"type": "record", "name": "LineageGraph", "fields": [{"name": "nodes", "type": {"type": "array", "items": {"type": "record", "name": "LineageNode", "fields": [{"name": "nodeId", "type": "string"}, {"name": "nodeType", "type": "string"}, {"name": "properties", "type": {"type": "map", "values": "string"}, "default": {}}]}}, "default": []}, {"name": "edges", "type": {"type": "array", "items": {"type": "record", "name": "LineageEdge", "fields": [{"name": "sourceNodeId", "type": "string"}, {"name": "targetNodeId", "type": "string"}, {"name": "edgeType", "type": "string"}, {"name": "weight", "type": "double", "default": 1.0}]}}, "default": []}]}], "default": null}]}], "default": null}, {"name": "validationResults", "type": {"type": "array", "items": {"type": "record", "name": "DataValidationResult", "fields": [{"name": "ruleId", "type": "string"}, {"name": "ruleName", "type": "string"}, {"name": "ruleCategory", "type": {"type": "enum", "name": "ValidationCategory", "symbols": ["SCHEMA_VALIDATION", "DATA_QUALITY", "BUSINESS_RULES", "SECURITY_COMPLIANCE", "ML_READINESS", "SEMANTIC_CONSISTENCY", "ETHICAL_COMPLIANCE", "PRIVACY_PROTECTION", "BIAS_DETECTION", "FAIRNESS_CHECK"]}, "default": "DATA_QUALITY"}, {"name": "passed", "type": "boolean"}, {"name": "severity", "type": {"type": "enum", "name": "ValidationSeverity", "symbols": ["INFO", "WARNING", "ERROR", "CRITICAL", "BLOCKING"]}, "default": "ERROR"}, {"name": "errorMessage", "type": ["null", "string"], "default": null}, {"name": "confidenceScore", "type": "double", "default": 1.0}, {"name": "details", "type": {"type": "map", "values": "string"}, "default": {}}, {"name": "suggestedFix", "type": ["null", "string"], "default": null}, {"name": "validatedAt", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "validatorVersion", "type": "string", "default": "unknown"}]}}, "default": []}, {"name": "agiMetadata", "type": {"type": "record", "name": "AGIMetadata", "fields": [{"name": "cognitiveComplexity", "type": "double", "default": 0.0, "doc": "Estimated cognitive complexity required to process this data"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "int", "default": 0, "doc": "Number of reasoning steps required"}, {"name": "multimodalityScore", "type": "double", "default": 0.0, "doc": "Score indicating multimodal nature of the data"}, {"name": "abstractionLevel", "type": {"type": "enum", "name": "AbstractionLevel", "symbols": ["RAW_SENSORY", "FEATURE_LEVEL", "CONCEPT_LEVEL", "ABSTRACT_REASONING", "META_COGNITIVE", "PHILOSOPHICAL"]}, "default": "FEATURE_LEVEL"}, {"name": "learningPotential", "type": "double", "default": 0.0, "doc": "Potential value for learning and model improvement"}, {"name": "noveltyScore", "type": "double", "default": 0.0, "doc": "How novel or unexpected this data is"}, {"name": "ethicalImplications", "type": {"type": "array", "items": "string"}, "default": [], "doc": "Identified ethical considerations"}, {"name": "biasIndicators", "type": {"type": "map", "values": "double"}, "default": {}, "doc": "Detected bias indicators and their scores"}, {"name": "uncertaintyMeasures", "type": {"type": "record", "name": "UncertaintyMeasures", "fields": [{"name": "epistemic", "type": "double", "default": 0.0, "doc": "Model uncertainty"}, {"name": "aleatoric", "type": "double", "default": 0.0, "doc": "Data uncertainty"}, {"name": "total", "type": "double", "default": 0.0, "doc": "Total uncertainty"}]}, "default": {"epistemic": 0.0, "aleatoric": 0.0, "total": 0.0}}, {"name": "explainabilityMetrics", "type": {"type": "record", "name": "ExplainabilityMetrics", "fields": [{"name": "interpretabilityScore", "type": "double", "default": 0.0}, {"name": "featureImportanceAvailable", "type": "boolean", "default": false}, {"name": "attentionMapsAvailable", "type": "boolean", "default": false}, {"name": "causalExplanationAvailable", "type": "boolean", "default": false}]}, "default": {"interpretabilityScore": 0.0, "featureImportanceAvailable": false, "attentionMapsAvailable": false, "causalExplanationAvailable": false}}]}, "default": {"cognitiveComplexity": 0.0, "reasoningDepth": 0, "multimodalityScore": 0.0, "abstractionLevel": "FEATURE_LEVEL", "learningPotential": 0.0, "noveltyScore": 0.0, "ethicalImplications": [], "biasIndicators": {}, "uncertaintyMeasures": {"epistemic": 0.0, "aleatoric": 0.0, "total": 0.0}, "explainabilityMetrics": {"interpretabilityScore": 0.0, "featureImportanceAvailable": false, "attentionMapsAvailable": false, "causalExplanationAvailable": false}}}, {"name": "schemaEvolution", "type": {"type": "record", "name": "SchemaEvolution", "fields": [{"name": "evolutionHistory", "type": {"type": "array", "items": {"type": "record", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "changeId", "type": "string"}, {"name": "changeType", "type": {"type": "enum", "name": "SchemaChangeType", "symbols": ["FIELD_ADDED", "FIELD_REMOVED", "FIELD_RENAMED", "TYPE_CHANGED", "ENUM_VALUE_ADDED", "ENUM_VALUE_REMOVED", "DEFAULT_CHANGED", "DOCUMENTATION_UPDATED"]}}, {"name": "fieldPath", "type": "string"}, {"name": "oldValue", "type": ["null", "string"], "default": null}, {"name": "newValue", "type": ["null", "string"], "default": null}, {"name": "changedAt", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "changedBy", "type": "string"}, {"name": "reason", "type": ["null", "string"], "default": null}]}}, "default": []}, {"name": "compatibilityLevel", "type": {"type": "enum", "name": "CompatibilityLevel", "symbols": ["BACKWARD", "FORWARD", "FULL", "NONE", "BACKWARD_TRANSITIVE", "FORWARD_TRANSITIVE", "FULL_TRANSITIVE"]}, "default": "BACKWARD"}, {"name": "migrationRequired", "type": "boolean", "default": false}, {"name": "migrationScript", "type": ["null", "string"], "default": null}]}, "default": {"evolutionHistory": [], "compatibilityLevel": "BACKWARD", "migrationRequired": false, "migrationScript": null}}]}