package com.asi.data_integration.grpc;

import com.asi.data_integration.grpc.EnhancedDataIntegrationGrpc;
import com.asi.data_integration.grpc.EnhancedDataIntegrationProto.*;
import com.asi.data_integration.processors.SemanticAnnotationProcessor;
import com.asi.data_integration.processors.MLFeatureProcessor;
import com.asi.data_integration.processors.AGIMetadataProcessor;
import com.asi.data_integration.processors.LineageProcessor;
import com.asi.data_integration.storage.DataRecordRepository;
import com.asi.data_integration.validation.AGIDataValidator;
import com.asi.data_integration.schema.SchemaEvolutionManager;
import com.asi.data_integration.monitoring.MetricsCollector;
import com.asi.data_integration.security.SecurityManager;

import io.grpc.stub.StreamObserver;
import io.grpc.Status;
import io.grpc.StatusException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Enhanced gRPC service implementation for AGI-capable data integration
 * 
 * Provides advanced capabilities for:
 * - Real-time data ingestion with AGI metadata
 * - Semantic annotation and enrichment
 * - ML feature extraction and processing
 * - Advanced lineage tracking
 * - AGI-specific data validation
 * - Schema evolution management
 */
@Service
public class EnhancedDataIntegrationServiceImpl extends EnhancedDataIntegrationGrpc.EnhancedDataIntegrationServiceImplBase {

    private static final Logger logger = LoggerFactory.getLogger(EnhancedDataIntegrationServiceImpl.class);

    @Autowired
    private SemanticAnnotationProcessor semanticProcessor;

    @Autowired
    private MLFeatureProcessor mlProcessor;

    @Autowired
    private AGIMetadataProcessor agiProcessor;

    @Autowired
    private LineageProcessor lineageProcessor;

    @Autowired
    private DataRecordRepository dataRepository;

    @Autowired
    private AGIDataValidator dataValidator;

    @Autowired
    private SchemaEvolutionManager schemaManager;

    @Autowired
    private MetricsCollector metricsCollector;

    @Autowired
    private SecurityManager securityManager;

    private final ExecutorService executorService = Executors.newFixedThreadPool(20);

    /**
     * Ingest data with advanced ML and semantic features
     */
    @Override
    public void ingestData(IngestDataRequest request, StreamObserver<IngestDataResponse> responseObserver) {
        String correlationId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        try {
            logger.info("Starting data ingestion with correlation ID: {}, records: {}", 
                       correlationId, request.getRecordsCount());

            // Security validation
            securityManager.validateIngestRequest(request);

            // Process records asynchronously
            List<CompletableFuture<DataRecord>> futures = request.getRecordsList().stream()
                .map(record -> processRecordAsync(record, request))
                .collect(Collectors.toList());

            // Wait for all processing to complete
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );

            allFutures.thenAccept(v -> {
                try {
                    List<DataRecord> processedRecords = futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());

                    // Store processed records
                    List<String> ingestedIds = dataRepository.saveAll(processedRecords);

                    // Collect validation results
                    List<DataValidationResult> validationResults = processedRecords.stream()
                        .flatMap(record -> record.getValidationResultsList().stream())
                        .collect(Collectors.toList());

                    // Build response
                    IngestDataResponse response = IngestDataResponse.newBuilder()
                        .addAllIngestedIds(ingestedIds)
                        .addAllValidationResults(validationResults)
                        .setSuccessCount(ingestedIds.size())
                        .setErrorCount(request.getRecordsCount() - ingestedIds.size())
                        .build();

                    // Record metrics
                    long processingTime = System.currentTimeMillis() - startTime;
                    metricsCollector.recordIngestion(correlationId, request.getRecordsCount(), 
                                                   ingestedIds.size(), processingTime);

                    responseObserver.onNext(response);
                    responseObserver.onCompleted();

                    logger.info("Data ingestion completed. Correlation ID: {}, Success: {}, Errors: {}", 
                               correlationId, ingestedIds.size(), request.getRecordsCount() - ingestedIds.size());

                } catch (Exception e) {
                    logger.error("Error completing data ingestion: " + e.getMessage(), e);
                    responseObserver.onError(Status.INTERNAL
                        .withDescription("Error completing data ingestion: " + e.getMessage())
                        .asRuntimeException());
                }
            }).exceptionally(throwable -> {
                logger.error("Error processing data ingestion: " + throwable.getMessage(), throwable);
                responseObserver.onError(Status.INTERNAL
                    .withDescription("Error processing data ingestion: " + throwable.getMessage())
                    .asRuntimeException());
                return null;
            });

        } catch (Exception e) {
            logger.error("Error in data ingestion: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error in data ingestion: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Process individual record asynchronously with AGI enhancements
     */
    private CompletableFuture<DataRecord> processRecordAsync(DataRecord record, IngestDataRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                DataRecord.Builder recordBuilder = record.toBuilder();

                // Step 1: Semantic annotation
                if (request.getGenerateEmbeddings()) {
                    SemanticAnnotations annotations = semanticProcessor.processRecord(record);
                    recordBuilder.setSemanticAnnotations(annotations);
                }

                // Step 2: ML feature extraction
                if (request.getExtractFeatures()) {
                    MLFeatures features = mlProcessor.extractFeatures(record);
                    recordBuilder.setMlFeatures(features);
                }

                // Step 3: AGI metadata generation
                AGIMetadata agiMetadata = agiProcessor.generateMetadata(record);
                recordBuilder.setAgiMetadata(agiMetadata);

                // Step 4: Update lineage
                DataLineage updatedLineage = lineageProcessor.updateLineage(
                    record.getLineage(), "DATA_INGESTION", "EnhancedDataIntegrationService"
                );
                recordBuilder.setLineage(updatedLineage);

                // Step 5: Validation
                if (request.getValidateSchema()) {
                    List<DataValidationResult> validationResults = dataValidator.validateRecord(recordBuilder.build());
                    recordBuilder.addAllValidationResults(validationResults);
                }

                return recordBuilder.build();

            } catch (Exception e) {
                logger.error("Error processing record {}: {}", record.getId(), e.getMessage(), e);
                throw new RuntimeException("Error processing record: " + e.getMessage(), e);
            }
        }, executorService);
    }

    /**
     * Query data with semantic and ML-based filtering
     */
    @Override
    public void queryData(QueryDataRequest request, StreamObserver<QueryDataResponse> responseObserver) {
        String correlationId = UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        try {
            logger.info("Starting data query with correlation ID: {}, query: {}", 
                       correlationId, request.getQuery());

            // Security validation
            securityManager.validateQueryRequest(request);

            // Execute query
            List<DataRecord> records = dataRepository.query(
                request.getQuery(),
                request.getFiltersList(),
                request.getLimit(),
                request.getOffset(),
                request.getIncludeEmbeddings(),
                request.getIncludeLineage()
            );

            // Build response
            QueryDataResponse response = QueryDataResponse.newBuilder()
                .addAllRecords(records)
                .setTotalCount(records.size())
                .setHasMore(records.size() == request.getLimit())
                .build();

            // Record metrics
            long processingTime = System.currentTimeMillis() - startTime;
            metricsCollector.recordQuery(correlationId, request.getQuery(), records.size(), processingTime);

            responseObserver.onNext(response);
            responseObserver.onCompleted();

            logger.info("Data query completed. Correlation ID: {}, Results: {}", correlationId, records.size());

        } catch (Exception e) {
            logger.error("Error in data query: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error in data query: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Stream real-time data with AGI metadata
     */
    @Override
    public void streamData(StreamDataRequest request, StreamObserver<DataRecord> responseObserver) {
        String correlationId = UUID.randomUUID().toString();

        try {
            logger.info("Starting data stream with correlation ID: {}, topics: {}", 
                       correlationId, request.getTopicsList());

            // Security validation
            securityManager.validateStreamRequest(request);

            // Start streaming
            dataRepository.streamData(
                request.getTopicsList(),
                request.getFiltersList(),
                request.getIncludeAgiMetadata(),
                record -> {
                    try {
                        responseObserver.onNext(record);
                    } catch (Exception e) {
                        logger.error("Error streaming record: " + e.getMessage(), e);
                        responseObserver.onError(Status.INTERNAL
                            .withDescription("Error streaming record: " + e.getMessage())
                            .asRuntimeException());
                    }
                },
                error -> {
                    logger.error("Error in data stream: " + error.getMessage(), error);
                    responseObserver.onError(Status.INTERNAL
                        .withDescription("Error in data stream: " + error.getMessage())
                        .asRuntimeException());
                },
                () -> {
                    logger.info("Data stream completed. Correlation ID: {}", correlationId);
                    responseObserver.onCompleted();
                }
            );

        } catch (Exception e) {
            logger.error("Error starting data stream: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error starting data stream: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Get semantic annotations for data
     */
    @Override
    public void getSemanticAnnotations(SemanticAnnotationRequest request, 
                                     StreamObserver<SemanticAnnotationResponse> responseObserver) {
        try {
            logger.info("Getting semantic annotations for data ID: {}", request.getDataId());

            DataRecord record = dataRepository.findById(request.getDataId());
            if (record == null) {
                responseObserver.onError(Status.NOT_FOUND
                    .withDescription("Data record not found: " + request.getDataId())
                    .asRuntimeException());
                return;
            }

            SemanticAnnotations annotations = semanticProcessor.processRecord(
                record, request.getAnnotationTypesList()
            );

            SemanticAnnotationResponse response = SemanticAnnotationResponse.newBuilder()
                .setAnnotations(annotations)
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            logger.error("Error getting semantic annotations: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error getting semantic annotations: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Perform ML feature extraction
     */
    @Override
    public void extractMLFeatures(MLFeatureRequest request, StreamObserver<MLFeatureResponse> responseObserver) {
        try {
            logger.info("Extracting ML features for data ID: {}", request.getDataId());

            DataRecord record = dataRepository.findById(request.getDataId());
            if (record == null) {
                responseObserver.onError(Status.NOT_FOUND
                    .withDescription("Data record not found: " + request.getDataId())
                    .asRuntimeException());
                return;
            }

            MLFeatures features = mlProcessor.extractFeatures(record, request.getFeatureTypesList());

            MLFeatureResponse response = MLFeatureResponse.newBuilder()
                .setFeatures(features)
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            logger.error("Error extracting ML features: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error extracting ML features: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Get data lineage graph
     */
    @Override
    public void getLineageGraph(LineageGraphRequest request, StreamObserver<LineageGraphResponse> responseObserver) {
        try {
            logger.info("Getting lineage graph for data ID: {}, depth: {}", 
                       request.getDataId(), request.getDepth());

            LineageGraph graph = lineageProcessor.getLineageGraph(
                request.getDataId(),
                request.getDepth(),
                request.getIncludeResourceUsage()
            );

            LineageGraphResponse response = LineageGraphResponse.newBuilder()
                .setGraph(graph)
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            logger.error("Error getting lineage graph: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error getting lineage graph: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Validate data against AGI requirements
     */
    @Override
    public void validateAGIData(AGIValidationRequest request, StreamObserver<AGIValidationResponse> responseObserver) {
        try {
            logger.info("Validating AGI data for record ID: {}", request.getRecord().getId());

            List<DataValidationResult> results = dataValidator.validateAGIRecord(
                request.getRecord(),
                request.getValidationRulesList()
            );

            boolean passed = results.stream().allMatch(DataValidationResult::getPassed);

            AGIValidationResponse response = AGIValidationResponse.newBuilder()
                .addAllResults(results)
                .setPassed(passed)
                .build();

            responseObserver.onNext(response);
            responseObserver.onCompleted();

        } catch (Exception e) {
            logger.error("Error validating AGI data: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error validating AGI data: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Update schema evolution
     */
    @Override
    public void updateSchemaEvolution(SchemaEvolutionRequest request, 
                                    StreamObserver<SchemaEvolutionResponse> responseObserver) {
        try {
            logger.info("Updating schema evolution: {}", request.getChange().getChangeType());

            SchemaEvolutionResponse.Builder responseBuilder = SchemaEvolutionResponse.newBuilder();

            if (request.getDryRun()) {
                // Dry run - validate changes without applying
                List<String> warnings = schemaManager.validateSchemaChange(request.getChange());
                responseBuilder.addAllWarnings(warnings);
                responseBuilder.setSuccess(warnings.isEmpty());
            } else {
                // Apply schema changes
                String migrationScript = schemaManager.applySchemaChange(request.getChange());
                responseBuilder.setMigrationScript(migrationScript);
                responseBuilder.setSuccess(true);
            }

            responseObserver.onNext(responseBuilder.build());
            responseObserver.onCompleted();

        } catch (Exception e) {
            logger.error("Error updating schema evolution: " + e.getMessage(), e);
            responseObserver.onError(Status.INTERNAL
                .withDescription("Error updating schema evolution: " + e.getMessage())
                .asRuntimeException());
        }
    }

    /**
     * Shutdown executor service gracefully
     */
    public void shutdown() {
        executorService.shutdown();
    }
}
