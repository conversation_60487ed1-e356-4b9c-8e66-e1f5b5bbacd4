package com.asi.data_integration.config

import scala.concurrent.duration.FiniteDuration
import java.time.Duration

/**
 * Configuration for AGI Data Processing
 * 
 * Comprehensive configuration for advanced AGI data processing capabilities
 * including semantic annotation, ML feature extraction, cognitive assessment,
 * and real-time streaming parameters.
 */
case class AGIProcessingConfig(
  // Core processing settings
  processingMode: String = "batch", // batch, streaming, hybrid
  processorVersion: String = "1.0.0",
  checkpointLocation: String = "/tmp/agi-checkpoint",
  
  // Feature enablement flags
  enableSemanticAnnotation: Boolean = true,
  enableMLFeatureExtraction: Boolean = true,
  enableAGIMetadata: Boolean = true,
  enableRelationshipEnhancement: Boolean = true,
  enableQualityValidation: Boolean = true,
  enableBiasDetection: Boolean = true,
  enableUncertaintyQuantification: Boolean = true,
  enableExplainabilityMetrics: Boolean = true,
  
  // Output destinations
  enableLearningEngineOutput: Boolean = true,
  enableDecisionEngineOutput: Boolean = true,
  enableKnowledgeGraphOutput: Boolean = true,
  enableSecurityOutput: Boolean = true,
  
  // Processing thresholds
  cognitiveComplexityThreshold: Double = 0.5,
  decisionComplexityThreshold: Double = 0.7,
  uncertaintyThreshold: Double = 0.8,
  biasThreshold: Double = 0.3,
  noveltyThreshold: Double = 0.6,
  
  // Semantic annotation settings
  semanticConfig: SemanticAnnotationConfig = SemanticAnnotationConfig(),
  
  // ML feature extraction settings
  mlConfig: MLFeatureConfig = MLFeatureConfig(),
  
  // AGI metadata settings
  agiConfig: AGIMetadataConfig = AGIMetadataConfig(),
  
  // Streaming settings
  streamingConfig: StreamingConfig = StreamingConfig(),
  
  // Kafka settings
  kafkaBootstrapServers: String = "localhost:9092",
  kafkaSchemaRegistryUrl: String = "http://localhost:8081",
  
  // Neo4j settings
  neo4jUri: String = "bolt://localhost:7687",
  neo4jUsername: String = "neo4j",
  neo4jPassword: String = "password",
  
  // Performance settings
  performanceConfig: PerformanceConfig = PerformanceConfig(),
  
  // Security settings
  securityConfig: SecurityConfig = SecurityConfig()
)

/**
 * Semantic annotation configuration
 */
case class SemanticAnnotationConfig(
  enableOntologyMapping: Boolean = true,
  enableEntityExtraction: Boolean = true,
  enableConceptAnnotation: Boolean = true,
  enableEmbeddingGeneration: Boolean = true,
  
  // Ontology settings
  ontologyEndpoints: List[String] = List(
    "http://dbpedia.org/sparql",
    "https://query.wikidata.org/sparql"
  ),
  ontologyTimeout: Duration = Duration.ofSeconds(30),
  
  // Entity extraction settings
  entityExtractionModels: List[String] = List(
    "en_core_web_sm",
    "en_core_web_lg"
  ),
  entityConfidenceThreshold: Double = 0.8,
  
  // Embedding settings
  embeddingModels: Map[String, EmbeddingModelConfig] = Map(
    "text" -> EmbeddingModelConfig(
      modelName = "sentence-transformers/all-MiniLM-L6-v2",
      dimension = 384,
      batchSize = 32
    ),
    "multimodal" -> EmbeddingModelConfig(
      modelName = "openai/clip-vit-base-patch32",
      dimension = 512,
      batchSize = 16
    )
  ),
  
  // Caching settings
  enableCaching: Boolean = true,
  cacheSize: Int = 10000,
  cacheTTL: Duration = Duration.ofHours(24)
)

case class EmbeddingModelConfig(
  modelName: String,
  dimension: Int,
  batchSize: Int = 32,
  maxSequenceLength: Int = 512,
  device: String = "auto" // auto, cpu, cuda
)

/**
 * ML feature extraction configuration
 */
case class MLFeatureConfig(
  enableFeatureVectors: Boolean = true,
  enableCategoricalFeatures: Boolean = true,
  enableNumericalFeatures: Boolean = true,
  enableTemporalFeatures: Boolean = true,
  enableFeatureEngineering: Boolean = true,
  
  // Feature extraction models
  featureExtractionModels: Map[String, String] = Map(
    "text" -> "distilbert-base-uncased",
    "image" -> "resnet50",
    "audio" -> "wav2vec2-base",
    "tabular" -> "xgboost"
  ),
  
  // Feature engineering settings
  enableScaling: Boolean = true,
  scalingMethod: String = "standard", // standard, minmax, robust
  enableDimensionalityReduction: Boolean = true,
  dimensionalityReductionMethod: String = "pca", // pca, umap, tsne
  targetDimension: Int = 100,
  
  // Feature selection settings
  enableFeatureSelection: Boolean = true,
  featureSelectionMethod: String = "mutual_info", // mutual_info, chi2, f_classif
  maxFeatures: Int = 1000,
  
  // Validation settings
  enableFeatureValidation: Boolean = true,
  featureValidationRules: List[String] = List(
    "no_null_values",
    "no_infinite_values",
    "variance_threshold"
  )
)

/**
 * AGI metadata configuration
 */
case class AGIMetadataConfig(
  enableCognitiveComplexity: Boolean = true,
  enableReasoningDepth: Boolean = true,
  enableMultimodalityScore: Boolean = true,
  enableLearningPotential: Boolean = true,
  enableNoveltyScore: Boolean = true,
  enableEthicalAnalysis: Boolean = true,
  enableBiasDetection: Boolean = true,
  enableUncertaintyQuantification: Boolean = true,
  enableExplainabilityMetrics: Boolean = true,
  
  // Cognitive complexity settings
  cognitiveComplexityModels: List[String] = List(
    "cognitive_complexity_transformer",
    "reasoning_depth_estimator"
  ),
  
  // Bias detection settings
  biasDetectionModels: Map[String, String] = Map(
    "gender" -> "gender_bias_detector",
    "race" -> "racial_bias_detector",
    "age" -> "age_bias_detector"
  ),
  biasDetectionThreshold: Double = 0.3,
  
  // Uncertainty quantification settings
  uncertaintyMethods: List[String] = List(
    "monte_carlo_dropout",
    "deep_ensembles",
    "bayesian_neural_networks"
  ),
  uncertaintyEstimationSamples: Int = 100,
  
  // Explainability settings
  explainabilityMethods: List[String] = List(
    "lime",
    "shap",
    "integrated_gradients",
    "attention_visualization"
  ),
  
  // Ethical analysis settings
  ethicalFrameworks: List[String] = List(
    "fairness_through_awareness",
    "individual_fairness",
    "demographic_parity",
    "equalized_odds"
  )
)

/**
 * Streaming configuration
 */
case class StreamingConfig(
  triggerInterval: String = "10 seconds",
  maxOffsetsPerTrigger: Long = 100000,
  
  // Watermarking settings
  enableWatermarking: Boolean = true,
  watermarkDelay: String = "1 minute",
  
  // State management
  enableStatefulProcessing: Boolean = true,
  stateTimeout: String = "1 hour",
  
  // Checkpointing
  checkpointInterval: String = "30 seconds",
  enableCheckpointCompression: Boolean = true,
  
  // Backpressure handling
  enableBackpressure: Boolean = true,
  maxBackpressureDelay: String = "5 minutes",
  
  // Error handling
  enableErrorRecovery: Boolean = true,
  maxRetries: Int = 3,
  retryDelay: String = "10 seconds"
)

/**
 * Performance configuration
 */
case class PerformanceConfig(
  // Spark settings
  sparkExecutorMemory: String = "4g",
  sparkExecutorCores: Int = 4,
  sparkExecutorInstances: Int = 10,
  sparkDriverMemory: String = "2g",
  sparkDriverCores: Int = 2,
  
  // Parallelism settings
  sparkSqlShufflePartitions: Int = 200,
  sparkDefaultParallelism: Int = 100,
  
  // Caching settings
  enableCaching: Boolean = true,
  cacheStorageLevel: String = "MEMORY_AND_DISK_SER",
  
  // Optimization settings
  enableAdaptiveQueryExecution: Boolean = true,
  enableDynamicPartitionPruning: Boolean = true,
  enableColumnPruning: Boolean = true,
  enablePredicatePushdown: Boolean = true,
  
  // Resource management
  enableDynamicAllocation: Boolean = true,
  dynamicAllocationMinExecutors: Int = 2,
  dynamicAllocationMaxExecutors: Int = 20,
  dynamicAllocationInitialExecutors: Int = 5,
  
  // Monitoring settings
  enableMetrics: Boolean = true,
  metricsNamespace: String = "asi.data_integration.agi",
  
  // Batch size settings
  batchSizeForMLInference: Int = 1000,
  batchSizeForSemanticAnnotation: Int = 500,
  batchSizeForFeatureExtraction: Int = 2000
)

/**
 * Security configuration
 */
case class SecurityConfig(
  enableEncryption: Boolean = true,
  encryptionAlgorithm: String = "AES-256-GCM",
  
  // Authentication settings
  enableAuthentication: Boolean = true,
  authenticationMethod: String = "kerberos", // kerberos, oauth2, jwt
  
  // Authorization settings
  enableAuthorization: Boolean = true,
  authorizationProvider: String = "ranger", // ranger, sentry, custom
  
  // Data masking settings
  enableDataMasking: Boolean = true,
  maskingRules: Map[String, String] = Map(
    "pii" -> "hash",
    "sensitive" -> "encrypt",
    "confidential" -> "redact"
  ),
  
  // Audit settings
  enableAuditLogging: Boolean = true,
  auditLogLevel: String = "INFO",
  auditLogDestination: String = "kafka", // kafka, file, database
  
  // Compliance settings
  enableComplianceChecking: Boolean = true,
  complianceFrameworks: List[String] = List(
    "GDPR",
    "CCPA",
    "HIPAA",
    "SOX"
  ),
  
  // Data lineage security
  enableLineageEncryption: Boolean = true,
  enableLineageDigitalSignature: Boolean = true
)

/**
 * Configuration validation
 */
object AGIProcessingConfig {
  
  def validate(config: AGIProcessingConfig): Either[List[String], AGIProcessingConfig] = {
    val errors = scala.collection.mutable.ListBuffer[String]()
    
    // Validate thresholds
    if (config.cognitiveComplexityThreshold < 0.0 || config.cognitiveComplexityThreshold > 1.0) {
      errors += "cognitiveComplexityThreshold must be between 0.0 and 1.0"
    }
    
    if (config.decisionComplexityThreshold < 0.0 || config.decisionComplexityThreshold > 1.0) {
      errors += "decisionComplexityThreshold must be between 0.0 and 1.0"
    }
    
    if (config.uncertaintyThreshold < 0.0 || config.uncertaintyThreshold > 1.0) {
      errors += "uncertaintyThreshold must be between 0.0 and 1.0"
    }
    
    // Validate processing mode
    if (!List("batch", "streaming", "hybrid").contains(config.processingMode)) {
      errors += "processingMode must be one of: batch, streaming, hybrid"
    }
    
    // Validate Kafka settings
    if (config.kafkaBootstrapServers.isEmpty) {
      errors += "kafkaBootstrapServers cannot be empty"
    }
    
    // Validate Neo4j settings
    if (config.neo4jUri.isEmpty) {
      errors += "neo4jUri cannot be empty"
    }
    
    // Validate performance settings
    if (config.performanceConfig.sparkExecutorCores < 1) {
      errors += "sparkExecutorCores must be at least 1"
    }
    
    if (config.performanceConfig.sparkExecutorInstances < 1) {
      errors += "sparkExecutorInstances must be at least 1"
    }
    
    if (errors.isEmpty) {
      Right(config)
    } else {
      Left(errors.toList)
    }
  }
  
  /**
   * Create default configuration for development environment
   */
  def development(): AGIProcessingConfig = {
    AGIProcessingConfig(
      processingMode = "batch",
      performanceConfig = PerformanceConfig(
        sparkExecutorMemory = "2g",
        sparkExecutorCores = 2,
        sparkExecutorInstances = 2,
        sparkDriverMemory = "1g"
      ),
      securityConfig = SecurityConfig(
        enableEncryption = false,
        enableAuthentication = false,
        enableAuthorization = false
      )
    )
  }
  
  /**
   * Create configuration for production environment
   */
  def production(): AGIProcessingConfig = {
    AGIProcessingConfig(
      processingMode = "streaming",
      performanceConfig = PerformanceConfig(
        sparkExecutorMemory = "8g",
        sparkExecutorCores = 8,
        sparkExecutorInstances = 20,
        sparkDriverMemory = "4g",
        enableDynamicAllocation = true
      ),
      securityConfig = SecurityConfig(
        enableEncryption = true,
        enableAuthentication = true,
        enableAuthorization = true,
        enableAuditLogging = true,
        enableComplianceChecking = true
      )
    )
  }
}
