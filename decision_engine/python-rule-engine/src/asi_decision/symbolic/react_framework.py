"""
ReAct (Reasoning and Acting) Framework

Implements the ReAct paradigm for combining reasoning and acting in language models.
Provides structured reasoning steps with action execution and observation integration.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json

from ..utils.logger import get_logger
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)

class ReActStepType(Enum):
    THOUGHT = "thought"
    ACTION = "action"
    OBSERVATION = "observation"
    REFLECTION = "reflection"

@dataclass
class ReActStep:
    """A single step in the ReAct reasoning process."""
    step_id: str
    step_type: ReActStepType
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    confidence: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "step_id": self.step_id,
            "step_type": self.step_type.value,
            "content": self.content,
            "metadata": self.metadata,
            "timestamp": self.timestamp,
            "confidence": self.confidence
        }

@dataclass
class ReActTrace:
    """Complete trace of ReAct reasoning process."""
    trace_id: str
    goal: str
    steps: List[ReActStep] = field(default_factory=list)
    final_answer: Optional[str] = None
    success: bool = False
    total_time: float = 0.0
    
    def add_step(self, step: ReActStep) -> None:
        """Add a step to the trace."""
        self.steps.append(step)
    
    def get_steps_by_type(self, step_type: ReActStepType) -> List[ReActStep]:
        """Get all steps of a specific type."""
        return [step for step in self.steps if step.step_type == step_type]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "trace_id": self.trace_id,
            "goal": self.goal,
            "steps": [step.to_dict() for step in self.steps],
            "final_answer": self.final_answer,
            "success": self.success,
            "total_time": self.total_time
        }

class ReActAgent:
    """
    ReAct agent that combines reasoning and acting.
    
    Implements the ReAct paradigm where the agent alternates between:
    1. Thought: Reasoning about the current situation
    2. Action: Taking an action based on reasoning
    3. Observation: Observing the results of the action
    4. Reflection: Reflecting on progress and next steps
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_steps = config.get("max_steps", 10)
        self.max_retries = config.get("max_retries", 3)
        self.reflection_frequency = config.get("reflection_frequency", 3)
        
        # Available actions
        self.actions: Dict[str, Callable] = {}
        self.metrics = MetricsCollector()
        
        # Reasoning templates
        self.thought_template = config.get("thought_template", 
            "Let me think about this step by step. {context}")
        self.action_template = config.get("action_template",
            "I need to {action} because {reasoning}")
        self.reflection_template = config.get("reflection_template",
            "So far I have {progress}. Next I should {next_step}")
    
    def register_action(self, name: str, action_func: Callable) -> None:
        """Register an available action."""
        self.actions[name] = action_func
        logger.info(f"Registered action: {name}")
    
    async def reason_and_act(self, goal: str, context: Dict[str, Any]) -> ReActTrace:
        """
        Execute the ReAct reasoning and acting process.
        
        Args:
            goal: The goal to achieve
            context: Initial context and information
            
        Returns:
            Complete ReAct trace with all steps
        """
        trace = ReActTrace(
            trace_id=str(uuid.uuid4()),
            goal=goal
        )
        
        start_time = time.time()
        current_context = context.copy()
        
        try:
            logger.info(f"Starting ReAct process for goal: {goal}")
            
            for step_num in range(self.max_steps):
                # 1. Thought step
                thought_step = await self._generate_thought(
                    goal, current_context, trace, step_num
                )
                trace.add_step(thought_step)
                
                # 2. Action step
                action_step = await self._generate_action(
                    goal, current_context, trace, step_num
                )
                trace.add_step(action_step)
                
                # 3. Execute action and observe
                observation_step = await self._execute_and_observe(
                    action_step, current_context
                )
                trace.add_step(observation_step)
                
                # Update context with observation
                current_context["last_observation"] = observation_step.content
                current_context["step_number"] = step_num + 1
                
                # 4. Reflection step (periodic)
                if (step_num + 1) % self.reflection_frequency == 0:
                    reflection_step = await self._generate_reflection(
                        goal, current_context, trace, step_num
                    )
                    trace.add_step(reflection_step)
                
                # Check if goal is achieved
                if await self._is_goal_achieved(goal, current_context, trace):
                    trace.success = True
                    trace.final_answer = await self._generate_final_answer(
                        goal, current_context, trace
                    )
                    break
            
            trace.total_time = time.time() - start_time
            
            # Record metrics
            self.metrics.record_react_trace(
                trace_id=trace.trace_id,
                steps=len(trace.steps),
                success=trace.success,
                total_time=trace.total_time
            )
            
            logger.info(f"ReAct process completed: {trace.success}")
            return trace
            
        except Exception as e:
            logger.error(f"ReAct process failed: {e}")
            trace.total_time = time.time() - start_time
            raise
    
    async def _generate_thought(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace, 
        step_num: int
    ) -> ReActStep:
        """Generate a thought step."""
        try:
            # Analyze current situation
            situation_analysis = self._analyze_situation(context, trace)
            
            # Generate thought content
            thought_content = self.thought_template.format(
                context=situation_analysis,
                goal=goal,
                step=step_num + 1
            )
            
            # In a real implementation, this would use an LLM
            # For now, we'll use rule-based reasoning
            thought_content = await self._reason_about_situation(
                goal, context, trace, step_num
            )
            
            return ReActStep(
                step_id=f"thought_{step_num}",
                step_type=ReActStepType.THOUGHT,
                content=thought_content,
                metadata={
                    "step_number": step_num,
                    "situation_analysis": situation_analysis
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate thought: {e}")
            return ReActStep(
                step_id=f"thought_{step_num}",
                step_type=ReActStepType.THOUGHT,
                content=f"Error in reasoning: {str(e)}",
                confidence=0.1
            )
    
    async def _generate_action(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace, 
        step_num: int
    ) -> ReActStep:
        """Generate an action step."""
        try:
            # Determine best action based on current state
            action_name, action_params = await self._select_action(
                goal, context, trace
            )
            
            action_content = f"{action_name}({json.dumps(action_params)})"
            
            return ReActStep(
                step_id=f"action_{step_num}",
                step_type=ReActStepType.ACTION,
                content=action_content,
                metadata={
                    "action_name": action_name,
                    "action_params": action_params,
                    "step_number": step_num
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate action: {e}")
            return ReActStep(
                step_id=f"action_{step_num}",
                step_type=ReActStepType.ACTION,
                content=f"no_action(error: {str(e)})",
                confidence=0.1
            )
    
    async def _execute_and_observe(
        self, 
        action_step: ReActStep, 
        context: Dict[str, Any]
    ) -> ReActStep:
        """Execute an action and observe the results."""
        try:
            action_name = action_step.metadata.get("action_name", "unknown")
            action_params = action_step.metadata.get("action_params", {})
            
            # Execute action if available
            if action_name in self.actions:
                result = await self.actions[action_name](action_params, context)
                observation_content = f"Action '{action_name}' executed successfully. Result: {result}"
            else:
                observation_content = f"Action '{action_name}' not available. Available actions: {list(self.actions.keys())}"
            
            return ReActStep(
                step_id=f"observation_{action_step.step_id}",
                step_type=ReActStepType.OBSERVATION,
                content=observation_content,
                metadata={
                    "action_executed": action_name,
                    "execution_success": action_name in self.actions
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to execute action: {e}")
            return ReActStep(
                step_id=f"observation_{action_step.step_id}",
                step_type=ReActStepType.OBSERVATION,
                content=f"Action execution failed: {str(e)}",
                confidence=0.1
            )
    
    async def _generate_reflection(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace, 
        step_num: int
    ) -> ReActStep:
        """Generate a reflection step."""
        try:
            # Analyze progress so far
            progress_analysis = self._analyze_progress(goal, trace)
            
            # Determine next steps
            next_steps = self._plan_next_steps(goal, context, trace)
            
            reflection_content = self.reflection_template.format(
                progress=progress_analysis,
                next_step=next_steps
            )
            
            return ReActStep(
                step_id=f"reflection_{step_num}",
                step_type=ReActStepType.REFLECTION,
                content=reflection_content,
                metadata={
                    "progress_analysis": progress_analysis,
                    "next_steps": next_steps,
                    "step_number": step_num
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to generate reflection: {e}")
            return ReActStep(
                step_id=f"reflection_{step_num}",
                step_type=ReActStepType.REFLECTION,
                content=f"Reflection error: {str(e)}",
                confidence=0.1
            )
    
    def _analyze_situation(self, context: Dict[str, Any], trace: ReActTrace) -> str:
        """Analyze the current situation."""
        recent_steps = trace.steps[-3:] if len(trace.steps) >= 3 else trace.steps
        
        analysis = f"Current step: {len(trace.steps) + 1}. "
        
        if recent_steps:
            last_step = recent_steps[-1]
            analysis += f"Last step was {last_step.step_type.value}: {last_step.content[:100]}..."
        
        if "last_observation" in context:
            analysis += f" Last observation: {context['last_observation'][:100]}..."
        
        return analysis
    
    async def _reason_about_situation(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace, 
        step_num: int
    ) -> str:
        """Reason about the current situation (simplified rule-based)."""
        # This would be replaced with LLM reasoning in production
        
        if step_num == 0:
            return f"I need to work towards the goal: {goal}. Let me start by understanding what I need to do."
        
        if "last_observation" in context:
            obs = context["last_observation"]
            if "successfully" in obs.lower():
                return "The last action was successful. I should continue with the next logical step."
            elif "failed" in obs.lower() or "error" in obs.lower():
                return "The last action failed. I need to try a different approach."
        
        return f"I'm making progress towards: {goal}. Let me think about what to do next."
    
    async def _select_action(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace
    ) -> tuple[str, Dict[str, Any]]:
        """Select the best action to take (simplified rule-based)."""
        # This would be replaced with more sophisticated action selection
        
        available_actions = list(self.actions.keys())
        
        if not available_actions:
            return "no_action", {}
        
        # Simple heuristic: rotate through available actions
        step_count = len(trace.steps)
        action_index = step_count % len(available_actions)
        selected_action = available_actions[action_index]
        
        return selected_action, {"context": context, "goal": goal}
    
    def _analyze_progress(self, goal: str, trace: ReActTrace) -> str:
        """Analyze progress towards the goal."""
        total_steps = len(trace.steps)
        successful_actions = len([
            step for step in trace.steps 
            if step.step_type == ReActStepType.OBSERVATION and "successfully" in step.content.lower()
        ])
        
        return f"completed {total_steps} steps with {successful_actions} successful actions"
    
    def _plan_next_steps(self, goal: str, context: Dict[str, Any], trace: ReActTrace) -> str:
        """Plan the next steps."""
        if len(trace.steps) < 3:
            return "continue with basic exploration and action execution"
        
        recent_failures = len([
            step for step in trace.steps[-3:] 
            if step.step_type == ReActStepType.OBSERVATION and "failed" in step.content.lower()
        ])
        
        if recent_failures >= 2:
            return "try a different approach as recent actions have been failing"
        else:
            return "continue with the current approach"
    
    async def _is_goal_achieved(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace
    ) -> bool:
        """Check if the goal has been achieved."""
        # Simple heuristic: goal achieved if we have enough successful steps
        successful_steps = len([
            step for step in trace.steps 
            if step.step_type == ReActStepType.OBSERVATION and "successfully" in step.content.lower()
        ])
        
        return successful_steps >= 3  # Arbitrary threshold
    
    async def _generate_final_answer(
        self, 
        goal: str, 
        context: Dict[str, Any], 
        trace: ReActTrace
    ) -> str:
        """Generate the final answer."""
        successful_actions = [
            step for step in trace.steps 
            if step.step_type == ReActStepType.OBSERVATION and "successfully" in step.content.lower()
        ]
        
        return f"Goal '{goal}' achieved through {len(successful_actions)} successful actions."

class ReActFramework:
    """
    Framework for managing multiple ReAct agents and orchestrating reasoning processes.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.agents: Dict[str, ReActAgent] = {}
        self.metrics = MetricsCollector()
    
    def create_agent(self, agent_id: str, agent_config: Dict[str, Any]) -> ReActAgent:
        """Create a new ReAct agent."""
        agent = ReActAgent(agent_config)
        self.agents[agent_id] = agent
        logger.info(f"Created ReAct agent: {agent_id}")
        return agent
    
    def get_agent(self, agent_id: str) -> Optional[ReActAgent]:
        """Get an existing agent."""
        return self.agents.get(agent_id)
    
    async def execute_reasoning(
        self, 
        agent_id: str, 
        goal: str, 
        context: Dict[str, Any]
    ) -> ReActTrace:
        """Execute reasoning using a specific agent."""
        agent = self.get_agent(agent_id)
        if not agent:
            raise ValueError(f"Agent {agent_id} not found")
        
        return await agent.reason_and_act(goal, context)
    
    def list_agents(self) -> List[str]:
        """List all available agents."""
        return list(self.agents.keys())
