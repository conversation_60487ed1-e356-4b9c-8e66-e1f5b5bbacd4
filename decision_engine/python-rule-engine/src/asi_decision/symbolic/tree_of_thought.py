"""
Tree-of-Thought (ToT) Planning

Implements Tree-of-Thought reasoning for complex problem solving.
Explores multiple reasoning paths simultaneously and selects the best approach.
"""

import asyncio
import logging
import time
import uuid
import math
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import heapq

from ..utils.logger import get_logger
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)

class ThoughtState(Enum):
    PENDING = "pending"
    EXPLORING = "exploring"
    EVALUATED = "evaluated"
    PRUNED = "pruned"
    SOLUTION = "solution"

@dataclass
class ThoughtNode:
    """A node in the Tree-of-Thought representing a reasoning step."""
    node_id: str
    parent_id: Optional[str]
    content: str
    depth: int
    state: ThoughtState = ThoughtState.PENDING
    value: float = 0.0
    confidence: float = 0.0
    children: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "node_id": self.node_id,
            "parent_id": self.parent_id,
            "content": self.content,
            "depth": self.depth,
            "state": self.state.value,
            "value": self.value,
            "confidence": self.confidence,
            "children": self.children,
            "metadata": self.metadata,
            "created_at": self.created_at
        }

@dataclass
class ToTSearchResult:
    """Result of Tree-of-Thought search."""
    best_path: List[ThoughtNode]
    all_nodes: Dict[str, ThoughtNode]
    total_nodes_explored: int
    search_time: float
    success: bool
    final_value: float

class TreeOfThought:
    """
    Tree-of-Thought reasoning engine.
    
    Implements systematic exploration of reasoning paths using tree search
    with evaluation, pruning, and backtracking capabilities.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_depth = config.get("max_depth", 5)
        self.max_nodes = config.get("max_nodes", 100)
        self.branching_factor = config.get("branching_factor", 3)
        self.pruning_threshold = config.get("pruning_threshold", 0.1)
        self.exploration_weight = config.get("exploration_weight", 1.4)
        
        # Search strategy
        self.search_strategy = config.get("search_strategy", "best_first")  # "best_first", "breadth_first", "depth_first"
        
        # Node storage
        self.nodes: Dict[str, ThoughtNode] = {}
        self.root_id: Optional[str] = None
        
        # Evaluation function
        self.evaluator: Optional[Callable] = None
        self.thought_generator: Optional[Callable] = None
        
        self.metrics = MetricsCollector()
    
    def set_evaluator(self, evaluator: Callable[[ThoughtNode, Dict[str, Any]], float]) -> None:
        """Set the evaluation function for nodes."""
        self.evaluator = evaluator
    
    def set_thought_generator(self, generator: Callable[[ThoughtNode, Dict[str, Any]], List[str]]) -> None:
        """Set the thought generation function."""
        self.thought_generator = generator
    
    async def search(self, initial_problem: str, context: Dict[str, Any]) -> ToTSearchResult:
        """
        Perform Tree-of-Thought search to solve a problem.
        
        Args:
            initial_problem: The problem statement
            context: Additional context and constraints
            
        Returns:
            Search result with best path and statistics
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting ToT search for: {initial_problem}")
            
            # Initialize root node
            self.root_id = str(uuid.uuid4())
            root_node = ThoughtNode(
                node_id=self.root_id,
                parent_id=None,
                content=initial_problem,
                depth=0,
                state=ThoughtState.EXPLORING
            )
            self.nodes[self.root_id] = root_node
            
            # Perform search based on strategy
            if self.search_strategy == "best_first":
                best_path = await self._best_first_search(context)
            elif self.search_strategy == "breadth_first":
                best_path = await self._breadth_first_search(context)
            elif self.search_strategy == "depth_first":
                best_path = await self._depth_first_search(context)
            else:
                raise ValueError(f"Unknown search strategy: {self.search_strategy}")
            
            search_time = time.time() - start_time
            
            # Determine success and final value
            success = len(best_path) > 0 and best_path[-1].state == ThoughtState.SOLUTION
            final_value = best_path[-1].value if best_path else 0.0
            
            result = ToTSearchResult(
                best_path=best_path,
                all_nodes=self.nodes.copy(),
                total_nodes_explored=len(self.nodes),
                search_time=search_time,
                success=success,
                final_value=final_value
            )
            
            # Record metrics
            self.metrics.record_tot_search(
                nodes_explored=len(self.nodes),
                max_depth=max(node.depth for node in self.nodes.values()),
                search_time=search_time,
                success=success
            )
            
            logger.info(f"ToT search completed: {success}, nodes: {len(self.nodes)}")
            return result
            
        except Exception as e:
            logger.error(f"ToT search failed: {e}")
            raise
    
    async def _best_first_search(self, context: Dict[str, Any]) -> List[ThoughtNode]:
        """Perform best-first search using a priority queue."""
        # Priority queue: (negative_value, node_id)
        priority_queue = [(-self.nodes[self.root_id].value, self.root_id)]
        visited = set()
        
        while priority_queue and len(self.nodes) < self.max_nodes:
            neg_value, current_id = heapq.heappop(priority_queue)
            
            if current_id in visited:
                continue
            
            visited.add(current_id)
            current_node = self.nodes[current_id]
            
            # Check if this is a solution
            if await self._is_solution(current_node, context):
                current_node.state = ThoughtState.SOLUTION
                return self._reconstruct_path(current_id)
            
            # Expand node if not at max depth
            if current_node.depth < self.max_depth:
                children = await self._expand_node(current_node, context)
                
                for child_id in children:
                    child_node = self.nodes[child_id]
                    if child_node.value > self.pruning_threshold:
                        heapq.heappush(priority_queue, (-child_node.value, child_id))
        
        # Return best path found so far
        best_node_id = max(self.nodes.keys(), key=lambda nid: self.nodes[nid].value)
        return self._reconstruct_path(best_node_id)
    
    async def _breadth_first_search(self, context: Dict[str, Any]) -> List[ThoughtNode]:
        """Perform breadth-first search."""
        queue = [self.root_id]
        visited = set()
        
        while queue and len(self.nodes) < self.max_nodes:
            current_id = queue.pop(0)
            
            if current_id in visited:
                continue
            
            visited.add(current_id)
            current_node = self.nodes[current_id]
            
            # Check if this is a solution
            if await self._is_solution(current_node, context):
                current_node.state = ThoughtState.SOLUTION
                return self._reconstruct_path(current_id)
            
            # Expand node if not at max depth
            if current_node.depth < self.max_depth:
                children = await self._expand_node(current_node, context)
                queue.extend(children)
        
        # Return best path found so far
        best_node_id = max(self.nodes.keys(), key=lambda nid: self.nodes[nid].value)
        return self._reconstruct_path(best_node_id)
    
    async def _depth_first_search(self, context: Dict[str, Any]) -> List[ThoughtNode]:
        """Perform depth-first search."""
        stack = [self.root_id]
        visited = set()
        
        while stack and len(self.nodes) < self.max_nodes:
            current_id = stack.pop()
            
            if current_id in visited:
                continue
            
            visited.add(current_id)
            current_node = self.nodes[current_id]
            
            # Check if this is a solution
            if await self._is_solution(current_node, context):
                current_node.state = ThoughtState.SOLUTION
                return self._reconstruct_path(current_id)
            
            # Expand node if not at max depth
            if current_node.depth < self.max_depth:
                children = await self._expand_node(current_node, context)
                stack.extend(reversed(children))  # Reverse to maintain left-to-right order
        
        # Return best path found so far
        best_node_id = max(self.nodes.keys(), key=lambda nid: self.nodes[nid].value)
        return self._reconstruct_path(best_node_id)
    
    async def _expand_node(self, node: ThoughtNode, context: Dict[str, Any]) -> List[str]:
        """Expand a node by generating child thoughts."""
        try:
            # Generate possible next thoughts
            if self.thought_generator:
                thought_contents = self.thought_generator(node, context)
            else:
                thought_contents = await self._default_thought_generation(node, context)
            
            # Limit branching factor
            thought_contents = thought_contents[:self.branching_factor]
            
            children = []
            for content in thought_contents:
                child_id = str(uuid.uuid4())
                child_node = ThoughtNode(
                    node_id=child_id,
                    parent_id=node.node_id,
                    content=content,
                    depth=node.depth + 1,
                    state=ThoughtState.EXPLORING
                )
                
                # Evaluate the child node
                if self.evaluator:
                    child_node.value = self.evaluator(child_node, context)
                else:
                    child_node.value = await self._default_evaluation(child_node, context)
                
                child_node.state = ThoughtState.EVALUATED
                
                # Add to storage
                self.nodes[child_id] = child_node
                node.children.append(child_id)
                children.append(child_id)
            
            return children
            
        except Exception as e:
            logger.error(f"Failed to expand node {node.node_id}: {e}")
            return []
    
    async def _default_thought_generation(self, node: ThoughtNode, context: Dict[str, Any]) -> List[str]:
        """Default thought generation (rule-based)."""
        # This would be replaced with LLM-based generation in production
        
        base_thoughts = [
            f"Let me approach this by breaking down: {node.content}",
            f"I should consider the constraints: {context.get('constraints', 'none')}",
            f"An alternative perspective on {node.content} might be...",
            f"The key insight here is that {node.content} relates to..."
        ]
        
        # Add depth-specific thoughts
        if node.depth == 0:
            base_thoughts.extend([
                "First, I need to understand the problem clearly",
                "Let me identify the key components of this problem"
            ])
        elif node.depth >= 2:
            base_thoughts.extend([
                "Now I should synthesize what I've learned",
                "Let me check if this approach is working"
            ])
        
        return base_thoughts[:self.branching_factor]
    
    async def _default_evaluation(self, node: ThoughtNode, context: Dict[str, Any]) -> float:
        """Default evaluation function."""
        # Simple heuristic evaluation
        base_value = 0.5
        
        # Reward depth (exploration)
        depth_bonus = 0.1 * node.depth
        
        # Reward certain keywords
        content_lower = node.content.lower()
        keyword_bonus = 0.0
        
        positive_keywords = ["solution", "answer", "conclusion", "result", "success"]
        negative_keywords = ["problem", "issue", "error", "fail", "stuck"]
        
        for keyword in positive_keywords:
            if keyword in content_lower:
                keyword_bonus += 0.2
        
        for keyword in negative_keywords:
            if keyword in content_lower:
                keyword_bonus -= 0.1
        
        # Add some randomness for exploration
        import random
        randomness = random.uniform(-0.1, 0.1)
        
        final_value = max(0.0, min(1.0, base_value + depth_bonus + keyword_bonus + randomness))
        return final_value
    
    async def _is_solution(self, node: ThoughtNode, context: Dict[str, Any]) -> bool:
        """Check if a node represents a solution."""
        # Simple heuristic: solution if high value and sufficient depth
        return (node.value > 0.8 and node.depth >= 2) or node.depth >= self.max_depth
    
    def _reconstruct_path(self, node_id: str) -> List[ThoughtNode]:
        """Reconstruct the path from root to a given node."""
        path = []
        current_id = node_id
        
        while current_id is not None:
            node = self.nodes[current_id]
            path.append(node)
            current_id = node.parent_id
        
        return list(reversed(path))
    
    def get_tree_statistics(self) -> Dict[str, Any]:
        """Get statistics about the current tree."""
        if not self.nodes:
            return {}
        
        depths = [node.depth for node in self.nodes.values()]
        values = [node.value for node in self.nodes.values()]
        
        return {
            "total_nodes": len(self.nodes),
            "max_depth": max(depths),
            "avg_depth": sum(depths) / len(depths),
            "max_value": max(values),
            "avg_value": sum(values) / len(values),
            "solution_nodes": len([n for n in self.nodes.values() if n.state == ThoughtState.SOLUTION])
        }
    
    def export_tree(self) -> Dict[str, Any]:
        """Export the entire tree structure."""
        return {
            "root_id": self.root_id,
            "nodes": {nid: node.to_dict() for nid, node in self.nodes.items()},
            "statistics": self.get_tree_statistics()
        }

class ToTPlanner:
    """
    High-level planner using Tree-of-Thought reasoning.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.tot_engine = TreeOfThought(config.get("tot_config", {}))
        self.metrics = MetricsCollector()
    
    async def plan(self, goal: str, constraints: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a plan to achieve a goal using ToT reasoning.
        
        Args:
            goal: The goal to achieve
            constraints: Constraints and context
            
        Returns:
            Planning result with steps and reasoning
        """
        try:
            logger.info(f"Planning for goal: {goal}")
            
            # Prepare context
            context = {
                "goal": goal,
                "constraints": constraints,
                "planning_mode": True
            }
            
            # Perform ToT search
            search_result = await self.tot_engine.search(goal, context)
            
            # Extract plan from best path
            plan_steps = []
            for i, node in enumerate(search_result.best_path):
                plan_steps.append({
                    "step": i + 1,
                    "description": node.content,
                    "confidence": node.confidence,
                    "value": node.value
                })
            
            planning_result = {
                "goal": goal,
                "plan_steps": plan_steps,
                "success": search_result.success,
                "confidence": search_result.final_value,
                "reasoning_tree": search_result.all_nodes,
                "statistics": {
                    "nodes_explored": search_result.total_nodes_explored,
                    "search_time": search_result.search_time
                }
            }
            
            logger.info(f"Planning completed: {search_result.success}")
            return planning_result
            
        except Exception as e:
            logger.error(f"Planning failed: {e}")
            raise
