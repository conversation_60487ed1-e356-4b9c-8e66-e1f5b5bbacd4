"""
Z3 SMT Solver Integration

Provides integration with the Z3 Satisfiability Modulo Theories (SMT) solver
for formal verification, constraint solving, and logical reasoning.
"""

import logging
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

try:
    import z3
    Z3_AVAILABLE = True
except ImportError:
    Z3_AVAILABLE = False
    z3 = None

from ..utils.logger import get_logger
from ..utils.metrics import MetricsCollector

logger = get_logger(__name__)

class ConstraintType(Enum):
    BOOLEAN = "boolean"
    INTEGER = "integer"
    REAL = "real"
    STRING = "string"
    ARRAY = "array"
    BITVECTOR = "bitvector"

class SolverResult(Enum):
    SAT = "satisfiable"
    UNSAT = "unsatisfiable"
    UNKNOWN = "unknown"
    ERROR = "error"

@dataclass
class Z3Variable:
    """Represents a Z3 variable."""
    name: str
    var_type: ConstraintType
    z3_var: Any = None
    domain: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if Z3_AVAILABLE and self.z3_var is None:
            self.z3_var = self._create_z3_variable()
    
    def _create_z3_variable(self):
        """Create the actual Z3 variable."""
        if self.var_type == ConstraintType.BOOLEAN:
            return z3.Bool(self.name)
        elif self.var_type == ConstraintType.INTEGER:
            return z3.Int(self.name)
        elif self.var_type == ConstraintType.REAL:
            return z3.Real(self.name)
        elif self.var_type == ConstraintType.STRING:
            return z3.String(self.name)
        elif self.var_type == ConstraintType.BITVECTOR:
            bits = self.domain.get("bits", 32) if self.domain else 32
            return z3.BitVec(self.name, bits)
        else:
            raise ValueError(f"Unsupported variable type: {self.var_type}")

@dataclass
class Z3Constraint:
    """Represents a Z3 constraint."""
    constraint_id: str
    description: str
    constraint_expr: str
    z3_constraint: Any = None
    variables: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Z3Model:
    """Represents a Z3 model (solution)."""
    model_id: str
    satisfiable: bool
    variable_assignments: Dict[str, Any] = field(default_factory=dict)
    solver_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

class Z3Solver:
    """
    Z3 SMT Solver wrapper for constraint solving and formal verification.
    
    Provides high-level interface for:
    - Variable declaration
    - Constraint specification
    - Model solving and verification
    - Optimization problems
    """
    
    def __init__(self, config: Dict[str, Any]):
        if not Z3_AVAILABLE:
            raise ImportError("Z3 solver not available. Install with: pip install z3-solver")
        
        self.config = config
        self.timeout = config.get("timeout", 30000)  # milliseconds
        self.logic = config.get("logic", "QF_LIA")  # Quantifier-free linear integer arithmetic
        
        # Z3 solver instance
        self.solver = z3.Solver()
        self.solver.set("timeout", self.timeout)
        
        # Variable and constraint tracking
        self.variables: Dict[str, Z3Variable] = {}
        self.constraints: Dict[str, Z3Constraint] = {}
        
        self.metrics = MetricsCollector()
        
        logger.info(f"Z3 Solver initialized with timeout: {self.timeout}ms")
    
    def declare_variable(
        self, 
        name: str, 
        var_type: ConstraintType, 
        domain: Optional[Dict[str, Any]] = None
    ) -> Z3Variable:
        """Declare a new variable."""
        try:
            if name in self.variables:
                logger.warning(f"Variable {name} already declared")
                return self.variables[name]
            
            variable = Z3Variable(
                name=name,
                var_type=var_type,
                domain=domain
            )
            
            self.variables[name] = variable
            logger.debug(f"Declared variable: {name} ({var_type.value})")
            
            return variable
            
        except Exception as e:
            logger.error(f"Failed to declare variable {name}: {e}")
            raise
    
    def add_constraint(
        self, 
        constraint_id: str, 
        description: str, 
        constraint_expr: str,
        variables: Optional[List[str]] = None
    ) -> Z3Constraint:
        """Add a constraint to the solver."""
        try:
            if constraint_id in self.constraints:
                logger.warning(f"Constraint {constraint_id} already exists")
                return self.constraints[constraint_id]
            
            # Parse and create Z3 constraint
            z3_constraint = self._parse_constraint(constraint_expr)
            
            constraint = Z3Constraint(
                constraint_id=constraint_id,
                description=description,
                constraint_expr=constraint_expr,
                z3_constraint=z3_constraint,
                variables=variables or []
            )
            
            # Add to solver
            self.solver.add(z3_constraint)
            self.constraints[constraint_id] = constraint
            
            logger.debug(f"Added constraint: {constraint_id}")
            return constraint
            
        except Exception as e:
            logger.error(f"Failed to add constraint {constraint_id}: {e}")
            raise
    
    def _parse_constraint(self, constraint_expr: str):
        """Parse constraint expression into Z3 constraint."""
        try:
            # Create a namespace with Z3 variables
            namespace = {"z3": z3}
            for var_name, variable in self.variables.items():
                namespace[var_name] = variable.z3_var
            
            # Evaluate the constraint expression
            z3_constraint = eval(constraint_expr, namespace)
            return z3_constraint
            
        except Exception as e:
            logger.error(f"Failed to parse constraint: {constraint_expr}")
            raise ValueError(f"Invalid constraint expression: {e}")
    
    def solve(self) -> Z3Model:
        """Solve the current set of constraints."""
        start_time = time.time()
        
        try:
            logger.info("Solving constraints...")
            
            # Check satisfiability
            result = self.solver.check()
            solve_time = time.time() - start_time
            
            if result == z3.sat:
                # Extract model
                z3_model = self.solver.model()
                variable_assignments = {}
                
                for var_name, variable in self.variables.items():
                    if z3_model[variable.z3_var] is not None:
                        value = z3_model[variable.z3_var]
                        # Convert Z3 value to Python value
                        if variable.var_type == ConstraintType.BOOLEAN:
                            variable_assignments[var_name] = bool(value)
                        elif variable.var_type in [ConstraintType.INTEGER, ConstraintType.BITVECTOR]:
                            variable_assignments[var_name] = int(str(value))
                        elif variable.var_type == ConstraintType.REAL:
                            variable_assignments[var_name] = float(str(value))
                        elif variable.var_type == ConstraintType.STRING:
                            variable_assignments[var_name] = str(value)
                        else:
                            variable_assignments[var_name] = str(value)
                
                model = Z3Model(
                    model_id=f"model_{int(time.time())}",
                    satisfiable=True,
                    variable_assignments=variable_assignments,
                    solver_time=solve_time
                )
                
                logger.info(f"Problem is satisfiable. Solution found in {solve_time:.3f}s")
                
            elif result == z3.unsat:
                model = Z3Model(
                    model_id=f"model_{int(time.time())}",
                    satisfiable=False,
                    solver_time=solve_time,
                    metadata={"result": "unsatisfiable"}
                )
                
                logger.info(f"Problem is unsatisfiable. Checked in {solve_time:.3f}s")
                
            else:  # unknown
                model = Z3Model(
                    model_id=f"model_{int(time.time())}",
                    satisfiable=False,
                    solver_time=solve_time,
                    metadata={"result": "unknown", "reason": str(self.solver.reason_unknown())}
                )
                
                logger.warning(f"Solver result unknown: {self.solver.reason_unknown()}")
            
            # Record metrics
            self.metrics.record_z3_solve(
                num_variables=len(self.variables),
                num_constraints=len(self.constraints),
                solve_time=solve_time,
                satisfiable=model.satisfiable
            )
            
            return model
            
        except Exception as e:
            logger.error(f"Solving failed: {e}")
            return Z3Model(
                model_id=f"error_{int(time.time())}",
                satisfiable=False,
                solver_time=time.time() - start_time,
                metadata={"error": str(e)}
            )
    
    def optimize(self, objective: str, maximize: bool = True) -> Z3Model:
        """Solve an optimization problem."""
        try:
            logger.info(f"Optimizing: {'maximize' if maximize else 'minimize'} {objective}")
            
            # Create optimizer
            optimizer = z3.Optimize()
            optimizer.set("timeout", self.timeout)
            
            # Add all constraints
            for constraint in self.constraints.values():
                optimizer.add(constraint.z3_constraint)
            
            # Parse objective
            namespace = {"z3": z3}
            for var_name, variable in self.variables.items():
                namespace[var_name] = variable.z3_var
            
            objective_expr = eval(objective, namespace)
            
            # Add objective
            if maximize:
                optimizer.maximize(objective_expr)
            else:
                optimizer.minimize(objective_expr)
            
            start_time = time.time()
            result = optimizer.check()
            solve_time = time.time() - start_time
            
            if result == z3.sat:
                z3_model = optimizer.model()
                variable_assignments = {}
                
                for var_name, variable in self.variables.items():
                    if z3_model[variable.z3_var] is not None:
                        value = z3_model[variable.z3_var]
                        variable_assignments[var_name] = self._convert_z3_value(value, variable.var_type)
                
                # Get objective value
                objective_value = z3_model.eval(objective_expr)
                
                model = Z3Model(
                    model_id=f"opt_model_{int(time.time())}",
                    satisfiable=True,
                    variable_assignments=variable_assignments,
                    solver_time=solve_time,
                    metadata={
                        "objective": objective,
                        "objective_value": str(objective_value),
                        "maximize": maximize
                    }
                )
                
                logger.info(f"Optimization completed. Objective value: {objective_value}")
                return model
            else:
                logger.warning("Optimization problem is unsatisfiable")
                return Z3Model(
                    model_id=f"opt_model_{int(time.time())}",
                    satisfiable=False,
                    solver_time=solve_time,
                    metadata={"result": str(result)}
                )
                
        except Exception as e:
            logger.error(f"Optimization failed: {e}")
            raise
    
    def _convert_z3_value(self, value, var_type: ConstraintType):
        """Convert Z3 value to Python value."""
        if var_type == ConstraintType.BOOLEAN:
            return bool(value)
        elif var_type in [ConstraintType.INTEGER, ConstraintType.BITVECTOR]:
            return int(str(value))
        elif var_type == ConstraintType.REAL:
            return float(str(value))
        elif var_type == ConstraintType.STRING:
            return str(value)
        else:
            return str(value)
    
    def verify_property(self, property_expr: str) -> bool:
        """Verify if a property holds for all models."""
        try:
            logger.info(f"Verifying property: {property_expr}")
            
            # Create negation of property
            namespace = {"z3": z3}
            for var_name, variable in self.variables.items():
                namespace[var_name] = variable.z3_var
            
            property_z3 = eval(property_expr, namespace)
            negated_property = z3.Not(property_z3)
            
            # Create temporary solver
            temp_solver = z3.Solver()
            temp_solver.set("timeout", self.timeout)
            
            # Add all constraints
            for constraint in self.constraints.values():
                temp_solver.add(constraint.z3_constraint)
            
            # Add negated property
            temp_solver.add(negated_property)
            
            # Check if negation is satisfiable
            result = temp_solver.check()
            
            if result == z3.unsat:
                logger.info("Property verification: VALID (property holds)")
                return True
            elif result == z3.sat:
                logger.info("Property verification: INVALID (counterexample exists)")
                return False
            else:
                logger.warning("Property verification: UNKNOWN")
                return False
                
        except Exception as e:
            logger.error(f"Property verification failed: {e}")
            return False
    
    def get_unsat_core(self) -> List[str]:
        """Get the unsatisfiable core of constraints."""
        try:
            # Enable unsat core tracking
            self.solver.set("unsat_core", True)
            
            # Add named constraints
            named_constraints = []
            for constraint_id, constraint in self.constraints.items():
                assertion = z3.Bool(f"assert_{constraint_id}")
                named_constraints.append((constraint_id, assertion))
                self.solver.add(z3.Implies(assertion, constraint.z3_constraint))
                self.solver.add(assertion)
            
            result = self.solver.check()
            
            if result == z3.unsat:
                core = self.solver.unsat_core()
                unsat_constraint_ids = []
                
                for constraint_id, assertion in named_constraints:
                    if assertion in core:
                        unsat_constraint_ids.append(constraint_id)
                
                logger.info(f"Unsat core: {unsat_constraint_ids}")
                return unsat_constraint_ids
            else:
                logger.info("Problem is satisfiable, no unsat core")
                return []
                
        except Exception as e:
            logger.error(f"Failed to get unsat core: {e}")
            return []
    
    def reset(self) -> None:
        """Reset the solver state."""
        self.solver.reset()
        self.constraints.clear()
        logger.info("Solver reset")
    
    def push(self) -> None:
        """Push current solver state onto stack."""
        self.solver.push()
    
    def pop(self) -> None:
        """Pop solver state from stack."""
        self.solver.pop()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get solver statistics."""
        return {
            "num_variables": len(self.variables),
            "num_constraints": len(self.constraints),
            "solver_statistics": str(self.solver.statistics()) if hasattr(self.solver, 'statistics') else {}
        }
