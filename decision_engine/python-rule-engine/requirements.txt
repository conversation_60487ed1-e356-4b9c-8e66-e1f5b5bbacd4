# ASI Decision Engine - Python Rule Engine Dependencies
# =====================================================

# Core dependencies
pydantic>=2.0.0
dataclasses-json>=0.6.0
typing-extensions>=4.5.0

# Async and concurrency
asyncio-mqtt>=0.13.0
aiofiles>=23.0.0
asyncio>=3.4.3

# gRPC and protobuf
grpcio>=1.54.0
grpcio-tools>=1.54.0
protobuf>=4.23.0
grpcio-reflection>=1.54.0

# Configuration management
PyYAML>=6.0
python-dotenv>=1.0.0
configparser>=5.3.0

# Logging and monitoring
structlog>=23.1.0
python-json-logger>=2.0.7
prometheus-client>=0.16.0
opentelemetry-api>=1.18.0
opentelemetry-sdk>=1.18.0
opentelemetry-instrumentation>=0.39b0

# Data processing and validation
numpy>=1.24.0
pandas>=2.0.0
jsonschema>=4.17.0
marshmallow>=3.19.0

# Caching and storage
redis>=4.5.0
diskcache>=5.6.0
sqlalchemy>=2.0.0
alembic>=1.11.0

# HTTP and API
fastapi>=0.100.0
uvicorn>=0.22.0
httpx>=0.24.0
requests>=2.31.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
factory-boy>=3.2.0

# Development tools
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.4.0
pre-commit>=3.3.0

# Performance monitoring
psutil>=5.9.0
memory-profiler>=0.60.0
line-profiler>=4.0.0

# Security
cryptography>=41.0.0
bcrypt>=4.0.0
passlib>=1.7.0

# Rule engine specific
pyparsing>=3.1.0
sympy>=1.12
networkx>=3.1

# Symbolic reasoning and logic
z3-solver>=4.12.0
pyswip>=0.2.10
python-constraint>=1.4.0

# Advanced planning algorithms
scipy>=1.10.0
anytree>=2.9.0

# ReAct and Tree-of-Thought reasoning
omegaconf>=2.3.0

# Machine learning integration (optional)
scikit-learn>=1.3.0
joblib>=1.3.0

# Time and date handling
python-dateutil>=2.8.0
pytz>=2023.3

# Utilities
click>=8.1.0
rich>=13.4.0
tqdm>=4.65.0
uuid>=1.30
