# ASI Memory Store Dependencies

# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Memory storage backends
redis==5.0.1
weaviate-client==3.25.3
chromadb==0.4.18

# Embedding and ML
torch==2.1.1
sentence-transformers==2.2.2
transformers==4.36.0
clip-by-openai==1.0
numpy==1.24.3
scikit-learn==1.3.2

# Database and storage
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# Async and networking
aioredis==2.0.1
httpx==0.25.2
websockets==12.0

# Monitoring and observability
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
structlog==23.2.0

# Configuration and utilities
pyyaml==6.0.1
python-dotenv==1.0.0
click==8.1.7
rich==13.7.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-benchmark==4.0.0
httpx==0.25.2

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
bandit==1.7.5
safety==2.3.5

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Development tools
ipython==8.17.2
jupyter==1.0.0
pre-commit==3.6.0

# gRPC
grpcio==1.59.3
grpcio-tools==1.59.3
protobuf==4.25.1

# Serialization
msgpack==1.0.7
orjson==3.9.10

# Caching
diskcache==5.6.3
cachetools==5.3.2

# Validation and security
validators==0.22.0
cryptography==41.0.8
passlib[bcrypt]==1.7.4

# Time and date handling
python-dateutil==2.8.2
pytz==2023.3

# File handling
aiofiles==23.2.1
pathlib2==2.3.7

# Metrics and profiling
psutil==5.9.6
memory-profiler==0.61.0

# Optional ML backends
# faiss-cpu==1.7.4  # Uncomment for FAISS support
# onnxruntime==1.16.3  # Uncomment for ONNX support
