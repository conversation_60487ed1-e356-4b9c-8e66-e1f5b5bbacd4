"""
Memory REST API

FastAPI-based REST API for memory operations in the ASI system.
Provides endpoints for storing, retrieving, searching, and managing memories.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid

from fastapi import FastAPI, HTTPException, Depends, Query, Path, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

from ...src.asi_memory.core.memory_manager import MemoryManager, MemoryFrame, MemoryType
from ...src.asi_memory.utils.logging_config import get_logger
from ..schemas.api_models import (
    MemoryFrameRequest,
    MemoryFrameResponse,
    SearchRequest,
    SearchResponse,
    MemoryStatsResponse
)

logger = get_logger(__name__)

# FastAPI app instance
app = FastAPI(
    title="ASI Memory Store API",
    description="REST API for ASI Memory Management System",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global memory manager instance
memory_manager: Optional[MemoryManager] = None

async def get_memory_manager() -> MemoryManager:
    """Dependency to get memory manager instance."""
    if memory_manager is None:
        raise HTTPException(status_code=503, detail="Memory Manager not initialized")
    return memory_manager

@app.on_event("startup")
async def startup_event():
    """Initialize memory manager on startup."""
    global memory_manager
    try:
        # Load configuration (in production, load from config file)
        config = {
            "redis": {"host": "localhost", "port": 6379},
            "weaviate": {"host": "localhost", "port": 8080},
            "chroma": {"host": "localhost", "port": 8000},
            "embeddings": {"text_model": "all-MiniLM-L6-v2"},
            "ttl": {"default_ttl": 3600},
            "archiver": {"storage_path": "./data/archive"}
        }
        
        memory_manager = MemoryManager(config)
        await memory_manager.initialize()
        logger.info("Memory Manager initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize Memory Manager: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    global memory_manager
    if memory_manager:
        await memory_manager.shutdown()
        logger.info("Memory Manager shutdown complete")

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "asi-memory-store"
    }

@app.post("/memories", response_model=MemoryFrameResponse)
async def store_memory(
    request: MemoryFrameRequest,
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Store a new memory frame."""
    try:
        # Convert request to MemoryFrame
        memory_frame = MemoryFrame(
            id=request.id or str(uuid.uuid4()),
            content=request.content,
            memory_type=MemoryType(request.memory_type),
            embedding=request.embedding,
            metadata=request.metadata or {},
            expires_at=request.expires_at,
            importance_score=request.importance_score,
            tags=request.tags
        )
        
        # Store memory
        memory_id = await manager.store_memory(memory_frame)
        
        # Retrieve stored memory for response
        stored_memory = await manager.retrieve_memory(memory_id)
        
        return MemoryFrameResponse.from_memory_frame(stored_memory)
        
    except Exception as e:
        logger.error(f"Error storing memory: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memories/{memory_id}", response_model=MemoryFrameResponse)
async def get_memory(
    memory_id: str = Path(..., description="Memory frame ID"),
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Retrieve a memory frame by ID."""
    try:
        memory_frame = await manager.retrieve_memory(memory_id)
        
        if not memory_frame:
            raise HTTPException(status_code=404, detail="Memory not found")
        
        return MemoryFrameResponse.from_memory_frame(memory_frame)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/memories/{memory_id}")
async def delete_memory(
    memory_id: str = Path(..., description="Memory frame ID"),
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Delete a memory frame."""
    try:
        deleted = await manager.delete_memory(memory_id)
        
        if not deleted:
            raise HTTPException(status_code=404, detail="Memory not found")
        
        return {"message": f"Memory {memory_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting memory {memory_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/memories/search", response_model=SearchResponse)
async def search_memories(
    request: SearchRequest,
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Search memories using semantic similarity."""
    try:
        # Convert memory types from strings to enums
        memory_types = None
        if request.memory_types:
            memory_types = [MemoryType(mt) for mt in request.memory_types]
        
        # Perform search
        results = await manager.search_memories(
            query=request.query,
            memory_types=memory_types,
            limit=request.limit,
            similarity_threshold=request.similarity_threshold
        )
        
        # Convert results to response format
        memory_responses = [
            MemoryFrameResponse.from_memory_frame(memory)
            for memory in results
        ]
        
        return SearchResponse(
            query=request.query,
            results=memory_responses,
            total_results=len(memory_responses)
        )
        
    except Exception as e:
        logger.error(f"Error searching memories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memories", response_model=List[MemoryFrameResponse])
async def list_memories(
    memory_type: Optional[str] = Query(None, description="Filter by memory type"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    manager: MemoryManager = Depends(get_memory_manager)
):
    """List memories with optional filtering and pagination."""
    try:
        # This would require implementing a list method in MemoryManager
        # For now, return empty list with appropriate message
        return []
        
    except Exception as e:
        logger.error(f"Error listing memories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats", response_model=MemoryStatsResponse)
async def get_memory_stats(
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Get memory system statistics."""
    try:
        stats = await manager.get_memory_stats()
        
        return MemoryStatsResponse(
            total_memories=stats["total_memories"],
            memory_types=stats["memory_types"],
            storage_backends=stats["storage_backends"],
            performance_metrics=stats["performance_metrics"]
        )
        
    except Exception as e:
        logger.error(f"Error getting memory stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/memories/batch", response_model=List[MemoryFrameResponse])
async def store_memories_batch(
    requests: List[MemoryFrameRequest],
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Store multiple memory frames in batch."""
    try:
        results = []
        
        for request in requests:
            memory_frame = MemoryFrame(
                id=request.id or str(uuid.uuid4()),
                content=request.content,
                memory_type=MemoryType(request.memory_type),
                embedding=request.embedding,
                metadata=request.metadata or {},
                expires_at=request.expires_at,
                importance_score=request.importance_score,
                tags=request.tags
            )
            
            memory_id = await manager.store_memory(memory_frame)
            stored_memory = await manager.retrieve_memory(memory_id)
            results.append(MemoryFrameResponse.from_memory_frame(stored_memory))
        
        return results
        
    except Exception as e:
        logger.error(f"Error storing batch memories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/memories/{memory_id}/similar")
async def get_similar_memories(
    memory_id: str = Path(..., description="Memory frame ID"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of results"),
    similarity_threshold: float = Query(0.7, ge=0.0, le=1.0, description="Similarity threshold"),
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Find memories similar to a given memory frame."""
    try:
        # Retrieve the source memory
        source_memory = await manager.retrieve_memory(memory_id)
        if not source_memory:
            raise HTTPException(status_code=404, detail="Memory not found")
        
        # Search for similar memories using the content
        if isinstance(source_memory.content, str):
            results = await manager.search_memories(
                query=source_memory.content,
                limit=limit + 1,  # +1 to exclude the source memory
                similarity_threshold=similarity_threshold
            )
            
            # Remove the source memory from results
            results = [r for r in results if r.id != memory_id][:limit]
            
            return [MemoryFrameResponse.from_memory_frame(memory) for memory in results]
        else:
            return []
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error finding similar memories: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

if __name__ == "__main__":
    uvicorn.run(
        "memory_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
