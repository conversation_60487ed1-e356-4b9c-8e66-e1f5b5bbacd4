"""
Code Generation Module for ASI Self-Improvement Engine

Enhanced code generation capabilities using TinyLlama/CodeLlama:
- Automated code generation and refactoring
- Sandbox validation and testing
- Version history and rollbacks
- GitOps integration for automated deployment
- Safety mechanisms and human oversight
"""

from .llm_code_generator import LLMCodeGenerator, CodeGenerationRequest, CodeGenerationResult
from .sandbox_validator import SandboxValidator, ValidationResult, SecurityCheck
from .version_manager import VersionManager, CodeVersion, RollbackManager
from .gitops_integration import GitOpsManager, GitOpsConfig, DeploymentPipeline
from .safety_monitor import SafetyMonitor, SafetyCheck, SafetyViolation

__all__ = [
    "LLMCodeGenerator",
    "CodeGenerationRequest",
    "CodeGenerationResult",
    "SandboxValidator",
    "ValidationResult", 
    "SecurityCheck",
    "VersionManager",
    "CodeVersion",
    "RollbackManager",
    "GitOpsManager",
    "GitOpsConfig",
    "DeploymentPipeline",
    "SafetyMonitor",
    "SafetyCheck",
    "SafetyViolation"
]
