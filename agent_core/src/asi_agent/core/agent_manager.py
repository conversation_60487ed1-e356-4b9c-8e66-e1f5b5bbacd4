"""
Agent Manager

Central orchestrator for all agent operations in the ASI system.
Manages agent lifecycle, coordination, resource allocation, and integration
with other ASI modules.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Any, Type, Union
from dataclasses import dataclass, field
from enum import Enum
import json

from ..agents.base_agent import BaseAgent
from ..agents.reasoning_agent import ReasoningAgent
from ..agents.learning_agent import LearningAgent
from ..agents.decision_agent import DecisionAgent
from ..orchestration.orchestrator import Orchestrator
from ..memory.context_manager import ContextManager
from ..goals.goal_manager import GoalManager
from ..audit.audit_logger import AuditLogger
from ..utils.validators import validate_agent_config
from ..utils.metrics import AgentMetrics
from ..utils.logging_config import get_logger

logger = get_logger(__name__)

class AgentStatus(Enum):
    CREATED = "created"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    STOPPING = "stopping"
    STOPPED = "stopped"

class AgentType(Enum):
    REASONING = "reasoning"
    LEARNING = "learning"
    DECISION = "decision"
    COORDINATION = "coordination"
    MONITORING = "monitoring"
    CUSTOM = "custom"

@dataclass
class AgentInfo:
    """Information about a managed agent."""
    id: str
    name: str
    agent_type: AgentType
    status: AgentStatus
    created_at: float
    last_activity: float
    task_count: int = 0
    error_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

class AgentManager:
    """
    Central agent management system for ASI.
    
    Coordinates agent lifecycle, resource allocation, task distribution,
    and integration with memory, learning, and decision systems.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.metrics = AgentMetrics()
        
        # Agent registry
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_info: Dict[str, AgentInfo] = {}
        
        # Agent type mappings
        self.agent_classes = {
            AgentType.REASONING: ReasoningAgent,
            AgentType.LEARNING: LearningAgent,
            AgentType.DECISION: DecisionAgent,
        }
        
        # Core components
        self.orchestrator = Orchestrator(config.get("orchestration", {}))
        self.context_manager = ContextManager(config.get("memory", {}))
        self.goal_manager = GoalManager(config.get("goals", {}))
        self.audit_logger = AuditLogger(config.get("audit", {}))
        
        # Configuration
        self.max_agents = config.get("max_agents", 100)
        self.default_timeout = config.get("default_timeout", 300)
        self.health_check_interval = config.get("health_check_interval", 30)
        
        # State
        self.is_initialized = False
        self._shutdown_event = asyncio.Event()
        
    async def initialize(self) -> None:
        """Initialize the agent manager and all components."""
        try:
            logger.info("Initializing Agent Manager...")
            
            # Initialize core components
            await self.orchestrator.initialize()
            await self.context_manager.initialize()
            await self.goal_manager.initialize()
            await self.audit_logger.initialize()
            
            # Start background tasks
            asyncio.create_task(self._health_monitor())
            asyncio.create_task(self._metrics_collector())
            
            self.is_initialized = True
            logger.info("Agent Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Agent Manager: {e}")
            raise
    
    async def create_agent(
        self,
        agent_type: AgentType,
        name: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new agent instance."""
        if not self.is_initialized:
            raise RuntimeError("Agent Manager not initialized")
        
        if len(self.agents) >= self.max_agents:
            raise RuntimeError(f"Maximum number of agents ({self.max_agents}) reached")
        
        try:
            # Generate agent ID and name
            agent_id = str(uuid.uuid4())
            agent_name = name or f"{agent_type.value}-{agent_id[:8]}"
            
            # Validate configuration
            agent_config = config or {}
            validate_agent_config(agent_config)
            
            # Create agent instance
            agent_class = self.agent_classes.get(agent_type)
            if not agent_class:
                raise ValueError(f"Unknown agent type: {agent_type}")
            
            agent = agent_class(
                agent_id=agent_id,
                name=agent_name,
                config=agent_config,
                context_manager=self.context_manager,
                audit_logger=self.audit_logger
            )
            
            # Initialize agent
            await agent.initialize()
            
            # Register agent
            self.agents[agent_id] = agent
            self.agent_info[agent_id] = AgentInfo(
                id=agent_id,
                name=agent_name,
                agent_type=agent_type,
                status=AgentStatus.ACTIVE,
                created_at=time.time(),
                last_activity=time.time()
            )
            
            # Log creation
            await self.audit_logger.log_agent_event(
                agent_id=agent_id,
                event_type="agent_created",
                details={
                    "agent_type": agent_type.value,
                    "name": agent_name,
                    "config": agent_config
                }
            )
            
            # Update metrics
            self.metrics.record_agent_created(agent_type.value)
            
            logger.info(f"Created agent {agent_name} ({agent_id})")
            return agent_id
            
        except Exception as e:
            self.metrics.record_error("create_agent", str(e))
            logger.error(f"Failed to create agent: {e}")
            raise
    
    async def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """Get an agent by ID."""
        return self.agents.get(agent_id)
    
    async def get_agent_info(self, agent_id: str) -> Optional[AgentInfo]:
        """Get agent information by ID."""
        return self.agent_info.get(agent_id)
    
    async def list_agents(
        self,
        agent_type: Optional[AgentType] = None,
        status: Optional[AgentStatus] = None
    ) -> List[AgentInfo]:
        """List agents with optional filtering."""
        agents = list(self.agent_info.values())
        
        if agent_type:
            agents = [a for a in agents if a.agent_type == agent_type]
        
        if status:
            agents = [a for a in agents if a.status == status]
        
        return agents
    
    async def execute_task(
        self,
        agent_id: str,
        task: Dict[str, Any],
        timeout: Optional[float] = None
    ) -> Any:
        """Execute a task on a specific agent."""
        if not self.is_initialized:
            raise RuntimeError("Agent Manager not initialized")
        
        agent = self.agents.get(agent_id)
        if not agent:
            raise ValueError(f"Agent {agent_id} not found")
        
        agent_info = self.agent_info[agent_id]
        if agent_info.status != AgentStatus.ACTIVE:
            raise RuntimeError(f"Agent {agent_id} is not active (status: {agent_info.status})")
        
        try:
            # Update agent status
            agent_info.status = AgentStatus.BUSY
            agent_info.last_activity = time.time()
            
            # Execute task with timeout
            task_timeout = timeout or self.default_timeout
            result = await asyncio.wait_for(
                agent.execute_task(task),
                timeout=task_timeout
            )
            
            # Update metrics
            agent_info.task_count += 1
            agent_info.status = AgentStatus.ACTIVE
            agent_info.last_activity = time.time()
            
            # Log task execution
            await self.audit_logger.log_task_execution(
                agent_id=agent_id,
                task=task,
                result=result,
                duration=time.time() - agent_info.last_activity
            )
            
            self.metrics.record_task_completed(agent_info.agent_type.value)
            
            return result
            
        except asyncio.TimeoutError:
            agent_info.status = AgentStatus.ERROR
            agent_info.error_count += 1
            self.metrics.record_task_timeout(agent_info.agent_type.value)
            logger.error(f"Task timeout for agent {agent_id}")
            raise
        except Exception as e:
            agent_info.status = AgentStatus.ERROR
            agent_info.error_count += 1
            self.metrics.record_task_error(agent_info.agent_type.value, str(e))
            logger.error(f"Task execution failed for agent {agent_id}: {e}")
            raise
    
    async def stop_agent(self, agent_id: str) -> bool:
        """Stop and remove an agent."""
        if agent_id not in self.agents:
            return False
        
        try:
            agent = self.agents[agent_id]
            agent_info = self.agent_info[agent_id]
            
            # Update status
            agent_info.status = AgentStatus.STOPPING
            
            # Stop agent
            await agent.shutdown()
            
            # Remove from registry
            del self.agents[agent_id]
            agent_info.status = AgentStatus.STOPPED
            
            # Log stopping
            await self.audit_logger.log_agent_event(
                agent_id=agent_id,
                event_type="agent_stopped",
                details={"reason": "manual_stop"}
            )
            
            self.metrics.record_agent_stopped(agent_info.agent_type.value)
            
            logger.info(f"Stopped agent {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop agent {agent_id}: {e}")
            return False
    
    async def broadcast_task(
        self,
        task: Dict[str, Any],
        agent_type: Optional[AgentType] = None,
        max_agents: Optional[int] = None
    ) -> Dict[str, Any]:
        """Broadcast a task to multiple agents."""
        # Get target agents
        target_agents = []
        for agent_id, info in self.agent_info.items():
            if agent_type and info.agent_type != agent_type:
                continue
            if info.status == AgentStatus.ACTIVE:
                target_agents.append(agent_id)
        
        if max_agents:
            target_agents = target_agents[:max_agents]
        
        # Execute task on all target agents
        results = {}
        tasks = []
        
        for agent_id in target_agents:
            task_coroutine = self.execute_task(agent_id, task)
            tasks.append((agent_id, task_coroutine))
        
        # Wait for all tasks to complete
        for agent_id, task_coroutine in tasks:
            try:
                result = await task_coroutine
                results[agent_id] = {"success": True, "result": result}
            except Exception as e:
                results[agent_id] = {"success": False, "error": str(e)}
        
        return results
    
    async def _health_monitor(self) -> None:
        """Background task to monitor agent health."""
        while not self._shutdown_event.is_set():
            try:
                current_time = time.time()
                
                for agent_id, info in self.agent_info.items():
                    # Check for inactive agents
                    if (current_time - info.last_activity) > (self.health_check_interval * 2):
                        if info.status == AgentStatus.ACTIVE:
                            info.status = AgentStatus.IDLE
                            logger.warning(f"Agent {agent_id} marked as idle")
                    
                    # Health check for active agents
                    if info.status in [AgentStatus.ACTIVE, AgentStatus.BUSY]:
                        agent = self.agents.get(agent_id)
                        if agent:
                            try:
                                health = await agent.health_check()
                                if not health.get("healthy", False):
                                    info.status = AgentStatus.ERROR
                                    logger.error(f"Agent {agent_id} failed health check")
                            except Exception as e:
                                info.status = AgentStatus.ERROR
                                logger.error(f"Health check failed for agent {agent_id}: {e}")
                
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"Error in health monitor: {e}")
                await asyncio.sleep(60)
    
    async def _metrics_collector(self) -> None:
        """Background task to collect and update metrics."""
        while not self._shutdown_event.is_set():
            try:
                # Collect agent statistics
                stats = {
                    "total_agents": len(self.agents),
                    "active_agents": len([a for a in self.agent_info.values() if a.status == AgentStatus.ACTIVE]),
                    "busy_agents": len([a for a in self.agent_info.values() if a.status == AgentStatus.BUSY]),
                    "error_agents": len([a for a in self.agent_info.values() if a.status == AgentStatus.ERROR]),
                }
                
                # Update metrics
                self.metrics.update_system_stats(stats)
                
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Error in metrics collector: {e}")
                await asyncio.sleep(60)
    
    async def get_system_stats(self) -> Dict[str, Any]:
        """Get comprehensive system statistics."""
        stats = {
            "total_agents": len(self.agents),
            "agent_status": {},
            "agent_types": {},
            "performance_metrics": self.metrics.get_summary()
        }
        
        # Count by status
        for info in self.agent_info.values():
            status = info.status.value
            stats["agent_status"][status] = stats["agent_status"].get(status, 0) + 1
            
            agent_type = info.agent_type.value
            stats["agent_types"][agent_type] = stats["agent_types"].get(agent_type, 0) + 1
        
        return stats
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the agent manager."""
        logger.info("Shutting down Agent Manager...")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Stop all agents
        for agent_id in list(self.agents.keys()):
            await self.stop_agent(agent_id)
        
        # Shutdown components
        await self.orchestrator.shutdown()
        await self.context_manager.shutdown()
        await self.goal_manager.shutdown()
        await self.audit_logger.shutdown()
        
        self.is_initialized = False
        logger.info("Agent Manager shutdown complete")
