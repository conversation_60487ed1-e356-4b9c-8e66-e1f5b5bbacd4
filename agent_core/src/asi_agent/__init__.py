"""
ASI Agent Core

Centralized AGI agent orchestration platform for the Artificial Super Intelligence (ASI) System.
Provides agent management, task graph execution, multi-agent coordination, context memory,
goal tracking, and comprehensive audit logging for intelligent agent operations.

Modules:
    core: Core agent management and execution engine
    agents: Specialized agent implementations
    orchestration: Multi-agent coordination and scheduling
    memory: Context and working memory management
    goals: Goal tracking and retry policies
    audit: Decision and action logging
    utils: Shared utilities and helpers
"""

__version__ = "1.0.0"
__author__ = "ASI Development Team"

from .core.agent_manager import Agent<PERSON>anager
from .core.task_graph import TaskGraph, TaskNode
from .core.execution_engine import ExecutionEngine
from .agents.base_agent import BaseAgent
from .agents.reasoning_agent import ReasoningAgent
from .agents.learning_agent import LearningAgent
from .agents.decision_agent import DecisionAgent
from .orchestration.orchestrator import Orchestrator
from .orchestration.scheduler import Scheduler
from .orchestration.coordinator import Coordinator
from .memory.context_manager import ContextManager
from .memory.working_memory import WorkingMemory
from .goals.goal_manager import GoalManager
from .goals.goal_tracker import GoalTracker
from .audit.audit_logger import AuditLogger

__all__ = [
    "AgentManager",
    "TaskGraph",
    "TaskNode", 
    "ExecutionEngine",
    "BaseAgent",
    "ReasoningAgent",
    "LearningAgent",
    "DecisionAgent",
    "Orchestrator",
    "Scheduler",
    "Coordinator",
    "ContextManager",
    "WorkingMemory",
    "GoalManager",
    "GoalTracker",
    "AuditLogger"
]
