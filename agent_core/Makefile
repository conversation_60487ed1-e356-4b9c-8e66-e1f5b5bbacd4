# ASI Agent Core Module Makefile
# Centralized AGI agent orchestration system for ASI

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
RESET := \033[0m

# Project configuration
PROJECT_NAME := asi-agent-core
VERSION := 1.0.0
PYTHON_VERSION := 3.9
RUST_VERSION := 1.70.0

# Directories
SRC_DIR := src
API_DIR := api
TEST_DIR := tests
CONFIG_DIR := configs
DOCKER_DIR := docker
K8S_DIR := k8s
SCRIPTS_DIR := scripts
RUST_DIR := rust-execution-engine

# Docker configuration
DOCKER_COMPOSE := $(DOCKER_DIR)/docker-compose.yml
DOCKERFILE := $(DOCKER_DIR)/Dockerfile

# Python configuration
PYTHON := python3
PIP := pip3
VENV := venv
REQUIREMENTS := requirements.txt

# Rust configuration
CARGO := cargo
RUST_TARGET := target

# Test configuration
PYTEST := pytest
PYTEST_ARGS := -v --cov=asi_agent --cov-report=html --cov-report=term
TEST_RESULTS_DIR := test-results

.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)ASI Agent Core Module$(RESET)"
	@echo "$(CYAN)=======================$(RESET)"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(BLUE)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

.PHONY: check-deps
check-deps: ## Check system dependencies
	@echo "$(BLUE)Checking system dependencies...$(RESET)"
	@command -v $(PYTHON) >/dev/null 2>&1 || { echo "$(RED)Python 3 is required$(RESET)"; exit 1; }
	@command -v $(CARGO) >/dev/null 2>&1 || { echo "$(RED)Rust/Cargo is required$(RESET)"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker is required$(RESET)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)Docker Compose is required$(RESET)"; exit 1; }
	@echo "$(GREEN)All dependencies satisfied$(RESET)"

.PHONY: setup
setup: check-deps ## Set up development environment
	@echo "$(BLUE)Setting up development environment...$(RESET)"
	
	# Create virtual environment
	@if [ ! -d "$(VENV)" ]; then \
		echo "Creating virtual environment..."; \
		$(PYTHON) -m venv $(VENV); \
	fi
	
	# Install Python dependencies
	@echo "Installing Python dependencies..."
	@. $(VENV)/bin/activate && $(PIP) install --upgrade pip
	@. $(VENV)/bin/activate && $(PIP) install -r $(REQUIREMENTS)
	@. $(VENV)/bin/activate && $(PIP) install -e .
	
	# Setup Rust environment
	@echo "Setting up Rust environment..."
	@cd $(RUST_DIR) && $(CARGO) build --release
	
	# Create necessary directories
	@mkdir -p $(TEST_RESULTS_DIR)
	@mkdir -p logs
	@mkdir -p data/agents
	@mkdir -p data/contexts
	@mkdir -p data/goals
	
	@echo "$(GREEN)Development environment setup complete$(RESET)"

.PHONY: install
install: setup ## Install the package
	@echo "$(BLUE)Installing ASI Agent Core...$(RESET)"
	@. $(VENV)/bin/activate && $(PIP) install -e .
	@echo "$(GREEN)Installation complete$(RESET)"

.PHONY: build
build: install ## Build all components
	@echo "$(BLUE)Building all components...$(RESET)"
	
	# Build Python package
	@echo "Building Python package..."
	@. $(VENV)/bin/activate && $(PYTHON) setup.py build
	
	# Build Rust execution engine
	@echo "Building Rust execution engine..."
	@cd $(RUST_DIR) && $(CARGO) build --release
	
	# Generate gRPC code
	@echo "Generating gRPC code..."
	@. $(VENV)/bin/activate && $(PYTHON) -m grpc_tools.protoc \
		-I $(API_DIR)/grpc/proto \
		--python_out=$(SRC_DIR)/asi_agent \
		--grpc_python_out=$(SRC_DIR)/asi_agent \
		$(API_DIR)/grpc/proto/*.proto
	
	# Build Python-Rust bindings
	@echo "Building Python-Rust bindings..."
	@cd $(RUST_DIR) && $(CARGO) build --release --features python-bindings
	
	@echo "$(GREEN)Build completed successfully$(RESET)"

.PHONY: test
test: ## Run all tests
	@echo "$(BLUE)Running tests...$(RESET)"
	
	# Python unit tests
	@echo "Running Python unit tests..."
	@. $(VENV)/bin/activate && $(PYTEST) $(TEST_DIR)/unit/ $(PYTEST_ARGS)
	
	# Python integration tests
	@echo "Running Python integration tests..."
	@. $(VENV)/bin/activate && $(PYTEST) $(TEST_DIR)/integration/ $(PYTEST_ARGS)
	
	# Rust tests
	@echo "Running Rust tests..."
	@cd $(RUST_DIR) && $(CARGO) test --release
	
	# Performance tests
	@echo "Running performance tests..."
	@. $(VENV)/bin/activate && $(PYTEST) $(TEST_DIR)/performance/ -v
	
	@echo "$(GREEN)All tests completed$(RESET)"

.PHONY: test-python
test-python: ## Run Python tests only
	@echo "$(BLUE)Running Python tests...$(RESET)"
	@. $(VENV)/bin/activate && $(PYTEST) $(TEST_DIR)/ $(PYTEST_ARGS)

.PHONY: test-rust
test-rust: ## Run Rust tests only
	@echo "$(BLUE)Running Rust tests...$(RESET)"
	@cd $(RUST_DIR) && $(CARGO) test --release

.PHONY: test-integration
test-integration: ## Run integration tests only
	@echo "$(BLUE)Running integration tests...$(RESET)"
	@. $(VENV)/bin/activate && $(PYTEST) $(TEST_DIR)/integration/ $(PYTEST_ARGS)

.PHONY: test-performance
test-performance: ## Run performance tests
	@echo "$(BLUE)Running performance tests...$(RESET)"
	@. $(VENV)/bin/activate && $(PYTEST) $(TEST_DIR)/performance/ -v --benchmark-only

.PHONY: lint
lint: ## Run code linting
	@echo "$(BLUE)Running code linting...$(RESET)"
	
	# Python linting
	@. $(VENV)/bin/activate && flake8 $(SRC_DIR) $(API_DIR) $(TEST_DIR)
	@. $(VENV)/bin/activate && black --check $(SRC_DIR) $(API_DIR) $(TEST_DIR)
	@. $(VENV)/bin/activate && isort --check-only $(SRC_DIR) $(API_DIR) $(TEST_DIR)
	
	# Rust linting
	@cd $(RUST_DIR) && $(CARGO) clippy -- -D warnings
	@cd $(RUST_DIR) && $(CARGO) fmt --check
	
	@echo "$(GREEN)Linting completed$(RESET)"

.PHONY: format
format: ## Format code
	@echo "$(BLUE)Formatting code...$(RESET)"
	
	# Python formatting
	@. $(VENV)/bin/activate && black $(SRC_DIR) $(API_DIR) $(TEST_DIR)
	@. $(VENV)/bin/activate && isort $(SRC_DIR) $(API_DIR) $(TEST_DIR)
	
	# Rust formatting
	@cd $(RUST_DIR) && $(CARGO) fmt
	
	@echo "$(GREEN)Code formatting completed$(RESET)"

.PHONY: build-docker
build-docker: ## Build Docker images
	@echo "$(BLUE)Building Docker images...$(RESET)"
	@docker-compose -f $(DOCKER_COMPOSE) build
	@echo "$(GREEN)Docker images built successfully$(RESET)"

.PHONY: start
start: ## Start all services
	@echo "$(BLUE)Starting ASI Agent Core services...$(RESET)"
	@docker-compose -f $(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)Services started successfully$(RESET)"
	@echo "$(YELLOW)Agent API available at: http://localhost:8001$(RESET)"
	@echo "$(YELLOW)Orchestration API: http://localhost:8002$(RESET)"
	@echo "$(YELLOW)Monitoring API: http://localhost:8003$(RESET)"

.PHONY: stop
stop: ## Stop all services
	@echo "$(BLUE)Stopping services...$(RESET)"
	@docker-compose -f $(DOCKER_COMPOSE) down
	@echo "$(GREEN)Services stopped$(RESET)"

.PHONY: restart
restart: stop start ## Restart all services

.PHONY: logs
logs: ## Show service logs
	@echo "$(BLUE)Showing service logs...$(RESET)"
	@docker-compose -f $(DOCKER_COMPOSE) logs -f

.PHONY: status
status: ## Show service status
	@echo "$(BLUE)Checking service status...$(RESET)"
	@docker-compose -f $(DOCKER_COMPOSE) ps
	@echo ""
	@echo "Health Checks:"
	@echo "-------------"
	@curl -s http://localhost:8001/health 2>/dev/null | jq . || echo "Agent API: Not responding"
	@curl -s http://localhost:8002/health 2>/dev/null | jq . || echo "Orchestration API: Not responding"
	@curl -s http://localhost:8003/health 2>/dev/null | jq . || echo "Monitoring API: Not responding"

.PHONY: deploy-k8s
deploy-k8s: ## Deploy to Kubernetes
	@echo "$(BLUE)Deploying to Kubernetes...$(RESET)"
	@kubectl apply -f $(K8S_DIR)/
	@echo "$(GREEN)Deployment completed$(RESET)"

.PHONY: undeploy-k8s
undeploy-k8s: ## Remove from Kubernetes
	@echo "$(BLUE)Removing from Kubernetes...$(RESET)"
	@kubectl delete -f $(K8S_DIR)/
	@echo "$(GREEN)Undeployment completed$(RESET)"

.PHONY: benchmark
benchmark: ## Run performance benchmarks
	@echo "$(BLUE)Running performance benchmarks...$(RESET)"
	@. $(VENV)/bin/activate && $(PYTHON) $(SCRIPTS_DIR)/benchmark.py
	@echo "$(GREEN)Benchmarks completed$(RESET)"

.PHONY: clean
clean: ## Clean build artifacts
	@echo "$(BLUE)Cleaning build artifacts...$(RESET)"
	@rm -rf build/
	@rm -rf dist/
	@rm -rf *.egg-info/
	@rm -rf $(TEST_RESULTS_DIR)/
	@rm -rf .pytest_cache/
	@rm -rf .coverage
	@rm -rf htmlcov/
	@find . -type d -name __pycache__ -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete
	@cd $(RUST_DIR) && $(CARGO) clean
	@echo "$(GREEN)Cleanup completed$(RESET)"

.PHONY: clean-all
clean-all: clean ## Clean everything including virtual environment
	@echo "$(BLUE)Cleaning everything...$(RESET)"
	@rm -rf $(VENV)/
	@docker-compose -f $(DOCKER_COMPOSE) down --volumes --remove-orphans
	@docker system prune -f
	@echo "$(GREEN)Complete cleanup finished$(RESET)"

.PHONY: docs
docs: ## Generate documentation
	@echo "$(BLUE)Generating documentation...$(RESET)"
	@. $(VENV)/bin/activate && sphinx-build -b html docs/ docs/_build/
	@cd $(RUST_DIR) && $(CARGO) doc --no-deps
	@echo "$(GREEN)Documentation generated$(RESET)"

.PHONY: security-scan
security-scan: ## Run security scans
	@echo "$(BLUE)Running security scans...$(RESET)"
	@. $(VENV)/bin/activate && bandit -r $(SRC_DIR)/
	@. $(VENV)/bin/activate && safety check
	@cd $(RUST_DIR) && $(CARGO) audit
	@echo "$(GREEN)Security scan completed$(RESET)"

.PHONY: dev
dev: ## Start development environment
	@echo "$(BLUE)Starting development environment...$(RESET)"
	@. $(VENV)/bin/activate && uvicorn api.rest.agent_api:app --reload --host 0.0.0.0 --port 8001

.PHONY: shell
shell: ## Start interactive shell
	@echo "$(BLUE)Starting interactive shell...$(RESET)"
	@. $(VENV)/bin/activate && $(PYTHON)

.PHONY: rust-shell
rust-shell: ## Start Rust development shell
	@echo "$(BLUE)Starting Rust development shell...$(RESET)"
	@cd $(RUST_DIR) && $(CARGO) run --bin shell

# Default target
.DEFAULT_GOAL := help
