"""
CLI Module

Provides command-line interface for the ASI Learning Engine:
- Training management
- Model fine-tuning
- Adapter management
- Meta-learning
- Continual learning
- Status monitoring
- Metrics retrieval
"""

from .main_cli import main_cli
from .training_cli import training_cli
from .adapter_cli import adapter_cli
from .meta_learning_cli import meta_learning_cli
from .continual_learning_cli import continual_learning_cli
from .monitoring_cli import monitoring_cli

__all__ = [
    "main_cli",
    "training_cli",
    "adapter_cli",
    "meta_learning_cli",
    "continual_learning_cli",
    "monitoring_cli"
]
