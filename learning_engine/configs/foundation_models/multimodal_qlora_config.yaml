# Foundation Model Configuration for Multimodal Model with QLoRA Fine-tuning
# Example configuration for fine-tuning a vision-language model using QLoRA

# Model configuration
model:
  model_name: "microsoft/git-base"
  model_type: "vision_text_dual_encoder"
  cache_dir: "./models/cache"
  torch_dtype: "float16"
  device_map: "auto"
  trust_remote_code: false
  revision: "main"

# Adapter configuration (QLoRA)
adapter:
  adapter_type: "qlora"
  target_modules: ["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
  rank: 64
  alpha: 128
  dropout: 0.05
  bias: "none"
  task_type: "FEATURE_EXTRACTION"
  inference_mode: false
  
  # QLoRA specific settings
  load_in_4bit: true
  bnb_4bit_compute_dtype: "float16"
  bnb_4bit_use_double_quant: true
  bnb_4bit_quant_type: "nf4"

# Quantization configuration
quantization:
  enabled: true
  method: "bitsandbytes"
  bits: 4
  compute_dtype: "float16"
  use_double_quant: true
  quant_type: "nf4"

# Training configuration
training:
  output_dir: "./results/multimodal_qlora"
  num_train_epochs: 5
  per_device_train_batch_size: 2
  per_device_eval_batch_size: 2
  gradient_accumulation_steps: 4
  learning_rate: 2e-4
  weight_decay: 0.01
  warmup_steps: 100
  logging_steps: 25
  save_steps: 1000
  eval_steps: 1000
  save_total_limit: 2
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  fp16: true
  bf16: false
  gradient_checkpointing: true
  dataloader_num_workers: 2
  remove_unused_columns: false
  label_smoothing_factor: 0.1

# Data configuration
data:
  dataset_name: "conceptual_captions"
  dataset_config: "unlabeled"
  train_file: null
  validation_file: null
  test_file: null
  text_column: "caption"
  max_length: 256
  preprocessing_num_workers: 8
  overwrite_cache: false
  validation_split_percentage: 10

# Meta-learning configuration
meta_learning:
  enabled: true
  algorithm: "maml"
  inner_lr: 0.01
  outer_lr: 0.001
  inner_steps: 3
  meta_batch_size: 2
  support_shots: 8
  query_shots: 16

# Continual learning configuration
continual_learning:
  enabled: true
  strategy: "ewc"
  regularization_strength: 5000.0
  memory_size: 2000
  rehearsal_enabled: true

# Experiment tracking
experiment_name: "multimodal_qlora_experiment"
run_name: "git_base_qlora_cc"
tags: ["multimodal", "qlora", "vision-language", "conceptual-captions"]

# Logging and monitoring
log_level: "INFO"
wandb_project: "asi_foundation_models"
tensorboard_log_dir: "./logs/tensorboard"
