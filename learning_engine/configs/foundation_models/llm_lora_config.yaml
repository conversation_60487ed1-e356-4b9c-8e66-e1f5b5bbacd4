# Foundation Model Configuration for LLM with LoRA Fine-tuning
# Example configuration for fine-tuning a large language model using LoRA

# Model configuration
model:
  model_name: "microsoft/DialoGPT-medium"
  model_type: "causal_lm"
  cache_dir: "./models/cache"
  torch_dtype: "auto"
  device_map: "auto"
  trust_remote_code: false
  revision: "main"

# Adapter configuration (LoRA)
adapter:
  adapter_type: "lora"
  target_modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
  rank: 16
  alpha: 32
  dropout: 0.1
  bias: "none"
  task_type: "CAUSAL_LM"
  inference_mode: false

# Quantization configuration
quantization:
  enabled: false
  method: "bitsandbytes"
  bits: 4
  compute_dtype: "float16"
  use_double_quant: true
  quant_type: "nf4"

# Training configuration
training:
  output_dir: "./results/llm_lora"
  num_train_epochs: 3
  per_device_train_batch_size: 4
  per_device_eval_batch_size: 4
  gradient_accumulation_steps: 1
  learning_rate: 5e-5
  weight_decay: 0.01
  warmup_steps: 500
  logging_steps: 10
  save_steps: 500
  eval_steps: 500
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  fp16: false
  bf16: false
  gradient_checkpointing: false
  dataloader_num_workers: 0
  remove_unused_columns: true
  label_smoothing_factor: 0.0

# Data configuration
data:
  dataset_name: null
  dataset_config: null
  train_file: "./data/train.jsonl"
  validation_file: "./data/validation.jsonl"
  test_file: "./data/test.jsonl"
  text_column: "text"
  max_length: 512
  preprocessing_num_workers: 4
  overwrite_cache: false
  validation_split_percentage: 5

# Meta-learning configuration
meta_learning:
  enabled: false
  algorithm: "maml"
  inner_lr: 0.01
  outer_lr: 0.001
  inner_steps: 5
  meta_batch_size: 4
  support_shots: 5
  query_shots: 15

# Continual learning configuration
continual_learning:
  enabled: false
  strategy: "ewc"
  regularization_strength: 1000.0
  memory_size: 1000
  rehearsal_enabled: false

# Experiment tracking
experiment_name: "llm_lora_experiment"
run_name: null
tags: ["llm", "lora", "fine-tuning"]

# Logging and monitoring
log_level: "INFO"
wandb_project: null
tensorboard_log_dir: "./logs/tensorboard"
